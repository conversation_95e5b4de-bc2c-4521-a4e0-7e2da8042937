"""
全球大学Master项目爬虫测试脚本
验证所有功能是否正常工作
"""

import os
import sys
import logging
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from demo_global_scraper import DemoGlobalScraper
from scrapers.global_university_data import QS_GLOBAL_UNIVERSITIES
from data_models import DataManager
import config


def test_data_models():
    """测试数据模型"""
    print("=" * 50)
    print("测试数据模型")
    print("=" * 50)
    
    try:
        # 创建数据管理器
        dm = DataManager()
        
        # 添加测试数据
        uni = dm.add_university("Test University", "Test Country", "Test Continent", 100, "https://test.edu")
        college = dm.add_college(uni.university_id, "Test College")
        program = dm.add_program(college.college_id, "Test Master Program", "Master")
        course = dm.add_course(program.program_id, "Test Course")
        
        # 验证数据
        assert len(dm.universities) == 1
        assert len(dm.colleges) == 1
        assert len(dm.programs) == 1
        assert len(dm.courses) == 1
        
        print("✅ 数据模型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False


def test_global_university_data():
    """测试全球大学数据"""
    print("=" * 50)
    print("测试全球大学数据")
    print("=" * 50)
    
    try:
        # 检查数据结构
        assert len(QS_GLOBAL_UNIVERSITIES) > 0
        
        # 检查数据字段
        required_fields = ['name', 'qs_ranking', 'country', 'continent', 'website']
        for uni in QS_GLOBAL_UNIVERSITIES[:5]:  # 检查前5所大学
            for field in required_fields:
                assert field in uni, f"缺少字段: {field}"
        
        # 检查排名顺序
        rankings = [uni['qs_ranking'] for uni in QS_GLOBAL_UNIVERSITIES[:10]]
        assert rankings == sorted(rankings), "排名顺序不正确"
        
        print(f"✅ 全球大学数据测试通过 (共{len(QS_GLOBAL_UNIVERSITIES)}所大学)")
        return True
        
    except Exception as e:
        print(f"❌ 全球大学数据测试失败: {e}")
        return False


def test_demo_scraper():
    """测试演示爬虫"""
    print("=" * 50)
    print("测试演示爬虫")
    print("=" * 50)
    
    try:
        # 创建演示爬虫
        scraper = DemoGlobalScraper()
        
        # 生成少量测试数据
        scraper.generate_demo_data(3)
        
        # 验证生成的数据
        assert len(scraper.data_manager.universities) == 3
        assert len(scraper.data_manager.colleges) > 0
        assert len(scraper.data_manager.programs) > 0
        assert len(scraper.data_manager.courses) > 0
        
        print("✅ 演示爬虫测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 演示爬虫测试失败: {e}")
        return False


def test_csv_files():
    """测试CSV文件生成"""
    print("=" * 50)
    print("测试CSV文件生成")
    print("=" * 50)
    
    try:
        # 检查文件是否存在
        csv_files = [
            config.UNIVERSITIES_CSV,
            config.COLLEGES_CSV,
            config.PROGRAMS_CSV,
            config.COURSES_CSV
        ]
        
        for csv_file in csv_files:
            assert os.path.exists(csv_file), f"文件不存在: {csv_file}"
            
            # 检查文件是否可读
            df = pd.read_csv(csv_file)
            assert len(df) > 0, f"文件为空: {csv_file}"
            
            print(f"✅ {os.path.basename(csv_file)}: {len(df)} 条记录")
        
        print("✅ CSV文件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ CSV文件测试失败: {e}")
        return False


def test_data_integrity():
    """测试数据完整性"""
    print("=" * 50)
    print("测试数据完整性")
    print("=" * 50)
    
    try:
        # 加载数据
        universities = pd.read_csv(config.UNIVERSITIES_CSV)
        colleges = pd.read_csv(config.COLLEGES_CSV)
        programs = pd.read_csv(config.PROGRAMS_CSV)
        courses = pd.read_csv(config.COURSES_CSV)
        
        # 检查外键关系
        # 学院的university_id应该在大学表中存在
        uni_ids = set(universities['university_id'])
        college_uni_ids = set(colleges['university_id'])
        assert college_uni_ids.issubset(uni_ids), "学院表中存在无效的university_id"
        
        # 专业的college_id应该在学院表中存在
        college_ids = set(colleges['college_id'])
        program_college_ids = set(programs['college_id'])
        assert program_college_ids.issubset(college_ids), "专业表中存在无效的college_id"
        
        # 课程的program_id应该在专业表中存在
        program_ids = set(programs['program_id'])
        course_program_ids = set(courses['program_id'])
        assert course_program_ids.issubset(program_ids), "课程表中存在无效的program_id"
        
        # 检查必填字段
        assert not universities['name'].isnull().any(), "大学名称不能为空"
        assert not programs['program_name'].isnull().any(), "专业名称不能为空"
        assert not courses['course_name'].isnull().any(), "课程名称不能为空"
        
        # 检查数据类型
        assert universities['qs_ranking'].dtype in ['int64', 'int32'], "QS排名应该是整数"
        assert programs['gpa_requirement'].dtype in ['float64', 'float32'], "GPA要求应该是浮点数"
        
        print("✅ 数据完整性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据完整性测试失败: {e}")
        return False


def test_analysis_functionality():
    """测试分析功能"""
    print("=" * 50)
    print("测试分析功能")
    print("=" * 50)
    
    try:
        # 检查分析报告是否生成
        report_file = 'data/analysis_report.md'
        assert os.path.exists(report_file), "分析报告文件不存在"
        
        # 检查报告内容
        with open(report_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert '数据概览' in content, "报告缺少数据概览"
            assert '专业学费分析' in content, "报告缺少学费分析"
            assert '热门专业' in content, "报告缺少热门专业分析"
        
        print("✅ 分析功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 分析功能测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("QS前1000全球大学Master项目爬虫 - 功能测试")
    print("=" * 60)
    
    tests = [
        ("数据模型", test_data_models),
        ("全球大学数据", test_global_university_data),
        ("演示爬虫", test_demo_scraper),
        ("CSV文件生成", test_csv_files),
        ("数据完整性", test_data_integrity),
        ("分析功能", test_analysis_functionality),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            failed += 1
        
        print()  # 空行分隔
    
    # 总结
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"✅ 通过: {passed} 个测试")
    print(f"❌ 失败: {failed} 个测试")
    print(f"总计: {passed + failed} 个测试")
    
    if failed == 0:
        print("\n🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查相关功能。")
        return False


def main():
    """主函数"""
    # 设置日志级别
    logging.getLogger().setLevel(logging.WARNING)  # 减少日志输出
    
    # 运行测试
    success = run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
