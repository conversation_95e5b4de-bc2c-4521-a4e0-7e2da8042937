"""
全球大学Master项目爬虫演示脚本
生成真实的数据结构，但不进行实际的网络爬取
"""

import argparse
import random
import pandas as pd
import sys
import os
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_models import DataManager
from scrapers.global_university_data import QS_GLOBAL_UNIVERSITIES
from scrapers.utils import setup_logging
import config


class DemoGlobalScraper:
    """演示版全球大学爬虫"""

    def __init__(self):
        """初始化演示爬虫"""
        self.logger = setup_logging()
        self.data_manager = DataManager()

        # 示例Master专业名称
        self.sample_master_programs = [
            "Master of Science in Computer Science",
            "Master of Engineering in Electrical Engineering",
            "Master of Business Administration",
            "Master of Arts in Economics",
            "Master of Science in Data Science",
            "Master of Engineering in Mechanical Engineering",
            "Master of Science in Artificial Intelligence",
            "Master of Arts in International Relations",
            "Master of Science in Biotechnology",
            "Master of Engineering in Civil Engineering",
            "Master of Science in Finance",
            "Master of Arts in Psychology",
            "Master of Science in Environmental Science",
            "Master of Engineering in Chemical Engineering",
            "Master of Science in Mathematics",
            "Master of Arts in Education",
            "Master of Science in Physics",
            "Master of Engineering in Aerospace Engineering",
            "Master of Science in Statistics",
            "Master of Arts in Linguistics"
        ]

        # 示例课程名称
        self.sample_courses = [
            "Advanced Algorithms",
            "Machine Learning",
            "Database Systems",
            "Software Engineering",
            "Computer Networks",
            "Artificial Intelligence",
            "Data Structures",
            "Operating Systems",
            "Computer Graphics",
            "Cybersecurity",
            "Research Methods",
            "Thesis Preparation",
            "Advanced Mathematics",
            "Statistics for Engineers",
            "Project Management"
        ]

        # 学院名称模板
        self.college_templates = [
            "School of Engineering",
            "College of Computer Science",
            "Business School",
            "School of Arts and Sciences",
            "Graduate School of Engineering",
            "Faculty of Science",
            "School of Medicine",
            "College of Liberal Arts",
            "School of Social Sciences",
            "Faculty of Engineering"
        ]

    def generate_demo_data(self, num_universities: int = 20) -> None:
        """
        生成演示数据

        Args:
            num_universities: 生成的大学数量
        """
        self.logger.info(f"开始生成 {num_universities} 所大学的演示数据...")

        # 选择前N所大学
        universities = QS_GLOBAL_UNIVERSITIES[:num_universities]

        for university_info in universities:
            self._generate_university_data(university_info)

        # 保存数据
        self._save_all_data()

        # 打印统计信息
        self._print_statistics()

        self.logger.info("演示数据生成完成!")

    def _generate_university_data(self, university_info: Dict[str, Any]) -> None:
        """
        为单个大学生成数据

        Args:
            university_info: 大学基本信息
        """
        # 1. 添加大学
        university = self.data_manager.add_university(
            name=university_info['name'],
            country=university_info['country'],
            continent=university_info['continent'],
            qs_ranking=university_info['qs_ranking'],
            website=university_info['website']
        )

        # 2. 生成学院
        num_colleges = random.randint(2, 5)
        colleges = []

        for i in range(num_colleges):
            college_name = random.choice(self.college_templates)
            # 确保学院名称唯一
            if college_name not in [c.college_name for c in colleges]:
                college = self.data_manager.add_college(
                    university_id=university.university_id,
                    college_name=college_name
                )
                colleges.append(college)

        # 3. 为每个学院生成Master项目
        for college in colleges:
            self._generate_master_programs(college.college_id)

    def _generate_master_programs(self, college_id: int) -> None:
        """
        为学院生成Master项目

        Args:
            college_id: 学院ID
        """
        num_programs = random.randint(3, 8)

        # 随机选择专业
        selected_programs = random.sample(self.sample_master_programs,
                                        min(num_programs, len(self.sample_master_programs)))

        for program_name in selected_programs:
            # 生成项目详细信息
            program = self.data_manager.add_program(
                college_id=college_id,
                program_name=program_name,
                degree="Master",
                duration=random.choice(["1 year", "1.5 years", "2 years"]),
                course_url=f"https://example.edu/programs/{program_name.lower().replace(' ', '-')}",
                gpa_requirement=round(random.uniform(3.0, 3.8), 1),
                intl_student_fee=round(random.uniform(25000, 60000), 2),
                language_req=random.choice([
                    "IELTS 6.5 / TOEFL 90",
                    "IELTS 7.0 / TOEFL 100",
                    "IELTS 7.5 / TOEFL 110"
                ]),
                intake_months=random.choice([
                    "Fall", "Spring", "Fall, Spring", "Fall, Spring, Summer"
                ]),
                application_deadline=random.choice([
                    "2025-01-15", "2025-03-01", "2025-04-30", "2025-06-15"
                ])
            )

            # 为每个项目生成课程
            self._generate_courses(program.program_id)

    def _generate_courses(self, program_id: int) -> None:
        """
        为专业生成课程

        Args:
            program_id: 专业ID
        """
        num_courses = random.randint(8, 15)

        # 随机选择课程
        selected_courses = random.sample(self.sample_courses,
                                       min(num_courses, len(self.sample_courses)))

        for i, course_name in enumerate(selected_courses):
            self.data_manager.add_course(
                program_id=program_id,
                course_code=f"CS{500 + i}",
                course_name=course_name,
                course_type=random.choice(["Core", "Elective", "Required"]),
                credit=random.choice([3.0, 4.0, 6.0]),
                description=f"Advanced course in {course_name.lower()}",
                course_url=f"https://example.edu/courses/{course_name.lower().replace(' ', '-')}"
            )

    def _save_all_data(self) -> None:
        """保存所有数据到CSV文件"""
        try:
            # 保存大学数据
            universities_df = pd.DataFrame(self.data_manager.get_universities_data())
            universities_df.to_csv(config.UNIVERSITIES_CSV, index=False, encoding='utf-8')
            self.logger.info(f"大学数据已保存到: {config.UNIVERSITIES_CSV}")

            # 保存学院数据
            colleges_df = pd.DataFrame(self.data_manager.get_colleges_data())
            colleges_csv = config.DATA_DIR + '/colleges.csv'
            colleges_df.to_csv(colleges_csv, index=False, encoding='utf-8')
            self.logger.info(f"学院数据已保存到: {colleges_csv}")

            # 保存专业数据
            programs_df = pd.DataFrame(self.data_manager.get_programs_data())
            programs_df.to_csv(config.PROGRAMS_CSV, index=False, encoding='utf-8')
            self.logger.info(f"专业数据已保存到: {config.PROGRAMS_CSV}")

            # 保存课程数据
            courses_df = pd.DataFrame(self.data_manager.get_courses_data())
            courses_df.to_csv(config.COURSES_CSV, index=False, encoding='utf-8')
            self.logger.info(f"课程数据已保存到: {config.COURSES_CSV}")

        except Exception as e:
            self.logger.error(f"保存数据时出错: {e}")

    def _print_statistics(self) -> None:
        """打印统计信息"""
        universities_count = len(self.data_manager.universities)
        colleges_count = len(self.data_manager.colleges)
        programs_count = len(self.data_manager.programs)
        courses_count = len(self.data_manager.courses)

        print("\n" + "="*60)
        print("演示数据生成统计")
        print("="*60)
        print(f"大学数量: {universities_count}")
        print(f"学院数量: {colleges_count}")
        print(f"Master项目数量: {programs_count}")
        print(f"课程数量: {courses_count}")

        if universities_count > 0:
            print(f"平均每所大学的学院数: {colleges_count / universities_count:.1f}")
            print(f"平均每所大学的Master项目数: {programs_count / universities_count:.1f}")
            print(f"平均每个项目的课程数: {courses_count / programs_count:.1f}")

        print("\n前5所大学:")
        for i, uni in enumerate(self.data_manager.universities[:5], 1):
            print(f"{i}. {uni.name} (QS排名: {uni.qs_ranking})")

        print("\n示例Master项目:")
        for i, program in enumerate(self.data_manager.programs[:5], 1):
            print(f"{i}. {program.program_name} - 学费: ${program.intl_student_fee:,.2f}")

        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='全球大学Master项目演示数据生成器')
    parser.add_argument('--universities', '-u', type=int, default=20,
                       help='生成的大学数量 (默认: 20)')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)

    scraper = DemoGlobalScraper()
    scraper.generate_demo_data(args.universities)


if __name__ == "__main__":
    main()
