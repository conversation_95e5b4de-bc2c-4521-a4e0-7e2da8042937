"""
爬虫工具函数
"""

import time
import random
import logging
import requests
from typing import Optional, Dict, Any
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

from config import USER_AGENTS, REQUEST_DELAY, TIMEOUT, MAX_RETRIES, SELENIUM_CONFIG


def setup_logging() -> logging.Logger:
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('data/scraper.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def get_random_user_agent() -> str:
    """获取随机User-Agent"""
    return random.choice(USER_AGENTS)


def random_delay():
    """随机延迟"""
    delay = random.uniform(*REQUEST_DELAY)
    time.sleep(delay)


def make_request(url: str, session: Optional[requests.Session] = None) -> Optional[requests.Response]:
    """
    发送HTTP请求，带重试机制

    Args:
        url: 目标URL
        session: 可选的requests session

    Returns:
        Response对象或None
    """
    logger = logging.getLogger(__name__)

    if session is None:
        session = requests.Session()

    headers = {
        'User-Agent': get_random_user_agent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    for attempt in range(MAX_RETRIES):
        try:
            logger.info(f"请求URL: {url} (尝试 {attempt + 1}/{MAX_RETRIES})")
            response = session.get(url, headers=headers, timeout=TIMEOUT)
            response.raise_for_status()
            random_delay()
            return response

        except requests.exceptions.RequestException as e:
            logger.warning(f"请求失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(2 ** attempt)  # 指数退避

    logger.error(f"所有请求尝试都失败了: {url}")
    return None


def parse_html(html_content: str) -> Optional[BeautifulSoup]:
    """
    解析HTML内容

    Args:
        html_content: HTML字符串

    Returns:
        BeautifulSoup对象或None
    """
    try:
        return BeautifulSoup(html_content, 'lxml')
    except Exception as e:
        logging.getLogger(__name__).error(f"HTML解析失败: {e}")
        return None


def setup_selenium_driver() -> Optional[webdriver.Chrome]:
    """
    设置Selenium Chrome驱动

    Returns:
        WebDriver对象或None
    """
    logger = logging.getLogger(__name__)

    try:
        chrome_options = Options()

        if SELENIUM_CONFIG['headless']:
            chrome_options.add_argument('--headless')

        chrome_options.add_argument(f'--window-size={SELENIUM_CONFIG["window_size"][0]},{SELENIUM_CONFIG["window_size"][1]}')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument(f'--user-agent={get_random_user_agent()}')

        # 使用项目中的chromedriver
        from selenium.webdriver.chrome.service import Service
        driver_path = './chromedriver'
        service = Service(executable_path=driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)

        driver.set_page_load_timeout(SELENIUM_CONFIG['page_load_timeout'])
        driver.implicitly_wait(SELENIUM_CONFIG['implicit_wait'])

        logger.info("Selenium Chrome驱动设置成功")
        return driver

    except Exception as e:
        logger.error(f"Selenium驱动设置失败: {e}")
        return None


def safe_selenium_get(driver: webdriver.Chrome, url: str) -> bool:
    """
    安全地使用Selenium获取页面

    Args:
        driver: WebDriver对象
        url: 目标URL

    Returns:
        是否成功获取页面
    """
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"Selenium访问: {url}")
        driver.get(url)
        random_delay()
        return True

    except (TimeoutException, WebDriverException) as e:
        logger.error(f"Selenium访问失败: {url}, 错误: {e}")
        return False


def extract_text_safe(element, default: str = "") -> str:
    """
    安全地提取元素文本

    Args:
        element: BeautifulSoup元素或Selenium元素
        default: 默认值

    Returns:
        提取的文本
    """
    try:
        if hasattr(element, 'get_text'):  # BeautifulSoup
            return element.get_text(strip=True)
        elif hasattr(element, 'text'):  # Selenium
            return element.text.strip()
        else:
            return str(element).strip()
    except:
        return default


def clean_text(text: str) -> str:
    """
    清理文本内容

    Args:
        text: 原始文本

    Returns:
        清理后的文本
    """
    if not text:
        return ""

    # 移除多余的空白字符
    text = ' '.join(text.split())

    # 移除特殊字符
    text = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')

    return text.strip()


def extract_number_from_text(text: str) -> Optional[float]:
    """
    从文本中提取数字

    Args:
        text: 包含数字的文本

    Returns:
        提取的数字或None
    """
    import re

    if not text:
        return None

    # 查找数字模式
    number_pattern = r'[\d,]+\.?\d*'
    matches = re.findall(number_pattern, text.replace(',', ''))

    if matches:
        try:
            return float(matches[0])
        except ValueError:
            pass

    return None
