"""
Master学位项目专用爬虫
专门爬取研究生Master学位项目信息
"""

import logging
import re
import time
import random
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from .utils import setup_logging, make_request, parse_html, extract_text_safe


class MasterProgramScraper:
    """Master学位项目爬虫类"""
    
    def __init__(self):
        """初始化爬虫"""
        self.logger = setup_logging()
        self.session = None
        
        # Master学位相关关键词
        self.master_keywords = [
            'master', 'masters', 'graduate', 'postgraduate', 'msc', 'ma', 'meng', 
            'mba', 'ms', 'taught', 'coursework', 'professional'
        ]
        
        # 学院相关关键词
        self.college_keywords = [
            'school', 'college', 'faculty', 'department', 'institute', 'center'
        ]
        
        # 专业领域关键词
        self.field_keywords = [
            'computer science', 'engineering', 'business', 'medicine', 'law',
            'arts', 'science', 'social science', 'humanities', 'education',
            'architecture', 'design', 'economics', 'finance', 'management'
        ]
    
    def find_master_programs(self, university_website: str) -> List[Dict[str, Any]]:
        """
        查找大学的Master学位项目
        
        Args:
            university_website: 大学官网URL
            
        Returns:
            Master项目信息列表
        """
        self.logger.info(f"开始查找Master项目: {university_website}")
        
        programs = []
        
        try:
            # 1. 查找研究生院/学院页面
            graduate_pages = self._find_graduate_pages(university_website)
            
            # 2. 在每个页面中查找Master项目
            for page_url in graduate_pages:
                page_programs = self._extract_programs_from_page(page_url)
                programs.extend(page_programs)
                
                # 添加延迟避免过于频繁的请求
                time.sleep(random.uniform(1, 3))
            
            # 3. 去重和过滤
            programs = self._filter_master_programs(programs)
            
        except Exception as e:
            self.logger.error(f"查找Master项目时出错: {e}")
        
        return programs
    
    def _find_graduate_pages(self, website: str) -> List[str]:
        """
        查找研究生相关页面
        
        Args:
            website: 大学网站URL
            
        Returns:
            研究生页面URL列表
        """
        graduate_pages = []
        
        try:
            response = make_request(website)
            if not response:
                return graduate_pages
            
            soup = parse_html(response.text)
            if not soup:
                return graduate_pages
            
            # 查找包含研究生关键词的链接
            links = soup.find_all('a', href=True)
            
            for link in links:
                href = link.get('href', '')
                text = extract_text_safe(link).lower()
                
                # 检查是否包含研究生相关关键词
                graduate_indicators = [
                    'graduate', 'postgraduate', 'masters', 'master', 'admissions',
                    'academics', 'programs', 'degrees', 'schools', 'colleges'
                ]
                
                if any(keyword in text or keyword in href.lower() for keyword in graduate_indicators):
                    full_url = urljoin(website, href)
                    if self._is_valid_url(full_url) and full_url not in graduate_pages:
                        graduate_pages.append(full_url)
            
            # 限制页面数量
            graduate_pages = graduate_pages[:20]
            
        except Exception as e:
            self.logger.error(f"查找研究生页面时出错: {e}")
        
        return graduate_pages
    
    def _extract_programs_from_page(self, page_url: str) -> List[Dict[str, Any]]:
        """
        从页面中提取Master项目信息
        
        Args:
            page_url: 页面URL
            
        Returns:
            项目信息列表
        """
        programs = []
        
        try:
            response = make_request(page_url)
            if not response:
                return programs
            
            soup = parse_html(response.text)
            if not soup:
                return programs
            
            # 查找项目信息
            # 方法1: 查找包含Master关键词的标题或链接
            program_elements = soup.find_all(['h1', 'h2', 'h3', 'h4', 'a', 'div'], 
                                           string=re.compile(r'master|msc|ma|meng|mba|ms', re.IGNORECASE))
            
            for element in program_elements:
                program_info = self._extract_program_details(element, page_url)
                if program_info:
                    programs.append(program_info)
            
            # 方法2: 查找程序列表
            program_lists = soup.find_all(['ul', 'ol', 'div'], class_=re.compile(r'program|degree|course', re.IGNORECASE))
            
            for program_list in program_lists:
                list_programs = self._extract_programs_from_list(program_list, page_url)
                programs.extend(list_programs)
            
        except Exception as e:
            self.logger.error(f"从页面提取项目信息时出错: {e}")
        
        return programs
    
    def _extract_program_details(self, element, base_url: str) -> Optional[Dict[str, Any]]:
        """
        从HTML元素中提取项目详细信息
        
        Args:
            element: HTML元素
            base_url: 基础URL
            
        Returns:
            项目信息字典或None
        """
        try:
            text = extract_text_safe(element)
            
            # 检查是否为Master项目
            if not any(keyword in text.lower() for keyword in self.master_keywords):
                return None
            
            # 提取项目名称
            program_name = self._clean_program_name(text)
            
            # 提取链接
            program_url = None
            if element.name == 'a' and element.get('href'):
                program_url = urljoin(base_url, element.get('href'))
            
            # 基础项目信息
            program_info = {
                'program_name': program_name,
                'degree': 'Master',
                'course_url': program_url,
                'duration': self._extract_duration(text),
                'gpa_requirement': None,
                'intl_student_fee': None,
                'language_req': None,
                'intake_months': None,
                'application_deadline': None
            }
            
            return program_info
            
        except Exception as e:
            self.logger.error(f"提取项目详细信息时出错: {e}")
            return None
    
    def _extract_programs_from_list(self, list_element, base_url: str) -> List[Dict[str, Any]]:
        """
        从列表元素中提取项目信息
        
        Args:
            list_element: 列表HTML元素
            base_url: 基础URL
            
        Returns:
            项目信息列表
        """
        programs = []
        
        try:
            # 查找列表项
            items = list_element.find_all(['li', 'div', 'a'])
            
            for item in items:
                text = extract_text_safe(item)
                
                # 检查是否包含Master关键词
                if any(keyword in text.lower() for keyword in self.master_keywords):
                    program_info = self._extract_program_details(item, base_url)
                    if program_info:
                        programs.append(program_info)
        
        except Exception as e:
            self.logger.error(f"从列表提取项目信息时出错: {e}")
        
        return programs
    
    def _filter_master_programs(self, programs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤和去重Master项目
        
        Args:
            programs: 原始项目列表
            
        Returns:
            过滤后的项目列表
        """
        filtered_programs = []
        seen_names = set()
        
        for program in programs:
            name = program.get('program_name', '').strip()
            
            # 去重
            if name in seen_names or not name:
                continue
            
            # 确保是Master项目
            if not any(keyword in name.lower() for keyword in self.master_keywords):
                continue
            
            seen_names.add(name)
            filtered_programs.append(program)
        
        return filtered_programs
    
    def _clean_program_name(self, text: str) -> str:
        """
        清理项目名称
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的项目名称
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除常见的前缀和后缀
        text = re.sub(r'^(program|degree|course):\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\s*(program|degree|course)$', '', text, flags=re.IGNORECASE)
        
        return text
    
    def _extract_duration(self, text: str) -> Optional[str]:
        """
        从文本中提取学制信息
        
        Args:
            text: 文本内容
            
        Returns:
            学制信息或None
        """
        # 查找年份信息
        duration_patterns = [
            r'(\d+)\s*year[s]?',
            r'(\d+)\s*yr[s]?',
            r'(\d+)\s*semester[s]?',
            r'(\d+)\s*month[s]?'
        ]
        
        for pattern in duration_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None
    
    def _is_valid_url(self, url: str) -> bool:
        """
        检查URL是否有效
        
        Args:
            url: URL字符串
            
        Returns:
            是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
