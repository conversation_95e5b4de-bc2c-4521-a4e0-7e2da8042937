"""
大学信息爬虫 - 爬取大学、专业和课程信息
"""

import logging
import pandas as pd
import requests
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
import re

from .utils import (
    setup_logging, make_request, parse_html, setup_selenium_driver,
    safe_selenium_get, extract_text_safe, clean_text, extract_number_from_text
)
from .qs_ranking_scraper import QSRankingScraper
from config import UNIVERSITIES_CSV, PROGRAMS_CSV, COURSES_CSV


class UniversityScraper:
    """大学信息爬虫类"""

    def __init__(self):
        """初始化爬虫"""
        self.logger = setup_logging()
        self.session = requests.Session()
        self.driver = None

        # 初始化QS排名爬虫
        self.qs_scraper = QSRankingScraper()
        self.universities_list = []

        # 数据存储
        self.universities_data = []
        self.programs_data = []
        self.courses_data = []

        # ID计数器
        self.university_id_counter = 1
        self.program_id_counter = 1
        self.course_id_counter = 1

    def __enter__(self):
        """上下文管理器入口"""
        self.driver = setup_selenium_driver()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.driver:
            self.driver.quit()
        self.session.close()

    def scrape_all_universities(self, max_ranking: int = 500) -> None:
        """
        爬取所有大学信息

        Args:
            max_ranking: 最大QS排名限制
        """
        self.logger.info("开始爬取QS前500美国大学信息...")

        # 获取QS美国大学列表
        self.universities_list = self.qs_scraper.get_qs_us_universities()
        self.universities_list = self.qs_scraper.filter_by_ranking(max_ranking)

        for university_info in self.universities_list:
            try:
                self.logger.info(f"正在处理: {university_info['name']}")
                self.scrape_university(university_info)

            except Exception as e:
                self.logger.error(f"处理大学 {university_info['name']} 时出错: {e}")
                continue

        self.save_to_csv()
        self.logger.info("所有大学信息爬取完成!")

    def scrape_university(self, university_info: Dict[str, Any]) -> None:
        """
        爬取单个大学的信息

        Args:
            university_info: 大学基本信息字典
        """
        university_id = self.university_id_counter
        self.university_id_counter += 1

        # 保存大学基本信息
        university_data = {
            'university_id': university_id,
            'name': university_info['name'],
            'country': university_info['country'],
            'continent': university_info['continent'],
            'qs_ranking': university_info['qs_ranking'],
            'website': university_info['website']
        }
        self.universities_data.append(university_data)

        # 爬取专业信息
        self.scrape_programs(university_id, university_info['website'])

    def scrape_programs(self, university_id: int, website: str) -> None:
        """
        爬取大学的专业信息

        Args:
            university_id: 大学ID
            website: 大学网站URL
        """
        self.logger.info(f"爬取大学ID {university_id} 的专业信息...")

        # 尝试找到专业/学院页面
        program_urls = self.find_program_pages(website)

        for program_url in program_urls:
            try:
                self.scrape_program_details(university_id, program_url)
            except Exception as e:
                self.logger.error(f"爬取专业页面 {program_url} 时出错: {e}")
                continue

    def find_program_pages(self, website: str) -> List[str]:
        """
        查找大学的专业页面链接

        Args:
            website: 大学网站URL

        Returns:
            专业页面URL列表
        """
        program_urls = []

        # 常见的专业页面关键词
        program_keywords = [
            'academics', 'programs', 'degrees', 'majors', 'schools', 'colleges',
            'graduate', 'undergraduate', 'admissions', 'study'
        ]

        try:
            response = make_request(website, self.session)
            if not response:
                return program_urls

            soup = parse_html(response.text)
            if not soup:
                return program_urls

            # 查找包含专业关键词的链接
            links = soup.find_all('a', href=True)

            for link in links:
                href = link.get('href', '')
                text = extract_text_safe(link).lower()

                # 检查链接文本或URL是否包含专业关键词
                if any(keyword in text or keyword in href.lower() for keyword in program_keywords):
                    full_url = urljoin(website, href)
                    if full_url not in program_urls and self.is_valid_url(full_url):
                        program_urls.append(full_url)

            # 限制数量避免过多请求
            program_urls = program_urls[:10]

        except Exception as e:
            self.logger.error(f"查找专业页面时出错: {e}")

        return program_urls

    def scrape_program_details(self, university_id: int, program_url: str) -> None:
        """
        爬取专业详细信息

        Args:
            university_id: 大学ID
            program_url: 专业页面URL
        """
        response = make_request(program_url, self.session)
        if not response:
            return

        soup = parse_html(response.text)
        if not soup:
            return

        # 尝试提取专业信息
        programs = self.extract_programs_from_page(soup, program_url)

        for program_info in programs:
            program_id = self.program_id_counter
            self.program_id_counter += 1

            program_data = {
                'program_id': program_id,
                'university_id': university_id,
                'name': program_info.get('name', ''),
                'related_majors': program_info.get('related_majors', ''),
                'duration': program_info.get('duration', ''),
                'course_url': program_info.get('course_url', program_url),
                'gpa_requirement': program_info.get('gpa_requirement'),
                'intl_student_fee': program_info.get('intl_student_fee'),
                'language_req': program_info.get('language_req', ''),
                'intake_months': program_info.get('intake_months', ''),
                'application_deadline': program_info.get('application_deadline', '')
            }

            self.programs_data.append(program_data)

            # 爬取该专业的课程信息
            self.scrape_courses(program_id, program_info.get('course_url', program_url))

    def extract_programs_from_page(self, soup, page_url: str) -> List[Dict[str, Any]]:
        """
        从页面中提取专业信息

        Args:
            soup: BeautifulSoup对象
            page_url: 页面URL

        Returns:
            专业信息列表
        """
        programs = []

        # 查找专业名称
        program_selectors = [
            'h1', 'h2', 'h3', '.program-title', '.degree-title',
            '.course-title', '[class*="program"]', '[class*="degree"]'
        ]

        for selector in program_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = extract_text_safe(element)
                if text and len(text) > 5 and self.is_likely_program_name(text):
                    program_info = {
                        'name': clean_text(text),
                        'course_url': page_url,
                        'duration': self.extract_duration(soup),
                        'gpa_requirement': self.extract_gpa_requirement(soup),
                        'intl_student_fee': self.extract_tuition_fee(soup),
                        'language_req': self.extract_language_requirements(soup),
                        'related_majors': self.extract_related_majors(text)
                    }
                    programs.append(program_info)
                    break  # 只取第一个匹配的专业名称

        # 如果没有找到专业，创建一个通用的
        if not programs:
            programs.append({
                'name': 'General Program',
                'course_url': page_url,
                'duration': '',
                'gpa_requirement': None,
                'intl_student_fee': None,
                'language_req': '',
                'related_majors': ''
            })

        return programs

    def is_likely_program_name(self, text: str) -> bool:
        """判断文本是否可能是专业名称"""
        program_indicators = [
            'degree', 'bachelor', 'master', 'phd', 'program', 'major',
            'science', 'arts', 'engineering', 'business', 'computer',
            'mathematics', 'physics', 'chemistry', 'biology', 'medicine'
        ]

        text_lower = text.lower()
        return any(indicator in text_lower for indicator in program_indicators)

    def extract_duration(self, soup) -> str:
        """提取学制信息"""
        duration_patterns = [
            r'(\d+)\s*year[s]?',
            r'(\d+)\s*semester[s]?',
            r'(\d+)\s*month[s]?'
        ]

        text = soup.get_text()
        for pattern in duration_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)

        return ''

    def extract_gpa_requirement(self, soup) -> Optional[float]:
        """提取GPA要求"""
        text = soup.get_text()
        gpa_pattern = r'gpa[:\s]*(\d+\.?\d*)'
        match = re.search(gpa_pattern, text, re.IGNORECASE)

        if match:
            try:
                return float(match.group(1))
            except ValueError:
                pass

        return None

    def extract_tuition_fee(self, soup) -> Optional[float]:
        """提取学费信息"""
        text = soup.get_text()
        fee_patterns = [
            r'\$[\d,]+',
            r'tuition[:\s]*\$?[\d,]+',
            r'fee[s]?[:\s]*\$?[\d,]+'
        ]

        for pattern in fee_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return extract_number_from_text(match.group(0))

        return None

    def extract_language_requirements(self, soup) -> str:
        """提取语言要求"""
        text = soup.get_text()
        lang_patterns = [
            r'ielts[:\s]*[\d\.]+',
            r'toefl[:\s]*\d+',
            r'english[:\s]*requirement[s]?'
        ]

        requirements = []
        for pattern in lang_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            requirements.extend(matches)

        return ' / '.join(requirements) if requirements else ''

    def extract_related_majors(self, program_name: str) -> str:
        """根据专业名称推断相关专业"""
        name_lower = program_name.lower()

        if 'computer' in name_lower or 'cs' in name_lower:
            return 'Computer Science,Software Engineering,Data Science'
        elif 'business' in name_lower or 'mba' in name_lower:
            return 'Business Administration,Management,Finance'
        elif 'engineering' in name_lower:
            return 'Engineering,Technology,Applied Sciences'
        elif 'science' in name_lower:
            return 'Natural Sciences,Research,STEM'

        return ''

    def scrape_courses(self, program_id: int, course_url: str) -> None:
        """
        爬取专业的课程信息

        Args:
            program_id: 专业ID
            course_url: 课程页面URL
        """
        # 简化版课程爬取 - 创建示例课程
        sample_courses = [
            {
                'course_name': 'Introduction to the Field',
                'course_type': 'Core',
                'credit': 3.0,
                'description': 'Foundational course introducing key concepts'
            },
            {
                'course_name': 'Advanced Topics',
                'course_type': 'Elective',
                'credit': 3.0,
                'description': 'Advanced study in specialized areas'
            },
            {
                'course_name': 'Research Methods',
                'course_type': 'Core',
                'credit': 4.0,
                'description': 'Research methodology and techniques'
            }
        ]

        for course_info in sample_courses:
            course_id = self.course_id_counter
            self.course_id_counter += 1

            course_data = {
                'course_id': course_id,
                'program_id': program_id,
                'course_code': f'PROG{program_id:03d}{course_id % 100:02d}',
                'course_name': course_info['course_name'],
                'course_type': course_info['course_type'],
                'credit': course_info['credit'],
                'description': course_info['description'],
                'course_url': course_url
            }

            self.courses_data.append(course_data)

    def is_valid_url(self, url: str) -> bool:
        """检查URL是否有效"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False

    def save_to_csv(self) -> None:
        """保存数据到CSV文件"""
        try:
            # 保存大学数据
            if self.universities_data:
                df_universities = pd.DataFrame(self.universities_data)
                df_universities.to_csv(UNIVERSITIES_CSV, index=False, encoding='utf-8')
                self.logger.info(f"大学数据已保存到: {UNIVERSITIES_CSV}")

            # 保存专业数据
            if self.programs_data:
                df_programs = pd.DataFrame(self.programs_data)
                df_programs.to_csv(PROGRAMS_CSV, index=False, encoding='utf-8')
                self.logger.info(f"专业数据已保存到: {PROGRAMS_CSV}")

            # 保存课程数据
            if self.courses_data:
                df_courses = pd.DataFrame(self.courses_data)
                df_courses.to_csv(COURSES_CSV, index=False, encoding='utf-8')
                self.logger.info(f"课程数据已保存到: {COURSES_CSV}")

        except Exception as e:
            self.logger.error(f"保存CSV文件时出错: {e}")


def main():
    """主函数"""
    with UniversityScraper() as scraper:
        scraper.scrape_all_universities()


if __name__ == "__main__":
    main()
