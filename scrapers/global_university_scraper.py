"""
全球大学信息爬虫
专门爬取QS前1000全球大学的Master学位项目信息
"""

import logging
import time
import random
import pandas as pd
from typing import List, Dict, Any, Optional
from .global_university_data import QS_GLOBAL_UNIVERSITIES
from .master_program_scraper import MasterProgramScraper
from .utils import setup_logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_models import DataManager
import config


class GlobalUniversityScraper:
    """全球大学信息爬虫类"""

    def __init__(self):
        """初始化爬虫"""
        self.logger = setup_logging()
        self.data_manager = DataManager()
        self.master_scraper = MasterProgramScraper()

        # 统计信息
        self.stats = {
            'universities_processed': 0,
            'programs_found': 0,
            'courses_found': 0,
            'errors': 0
        }

    def scrape_global_universities(self, max_ranking: int = 1000,
                                 limit: Optional[int] = None) -> None:
        """
        爬取全球大学信息

        Args:
            max_ranking: 最大QS排名限制
            limit: 限制爬取的大学数量（用于测试）
        """
        self.logger.info(f"开始爬取QS前{max_ranking}全球大学的Master项目信息...")

        # 获取大学列表
        universities = self._get_universities_list(max_ranking)

        if limit:
            universities = universities[:limit]
            self.logger.info(f"限制爬取数量为: {limit}")

        self.logger.info(f"共需要处理 {len(universities)} 所大学")

        # 逐个处理大学
        for i, university_info in enumerate(universities, 1):
            try:
                self.logger.info(f"[{i}/{len(universities)}] 正在处理: {university_info['name']}")
                self._scrape_single_university(university_info)
                self.stats['universities_processed'] += 1

                # 添加延迟避免过于频繁的请求
                delay = random.uniform(*config.REQUEST_DELAY)
                time.sleep(delay)

            except Exception as e:
                self.logger.error(f"处理大学 {university_info['name']} 时出错: {e}")
                self.stats['errors'] += 1
                continue

        # 保存数据
        self._save_all_data()

        # 打印统计信息
        self._print_statistics()

        self.logger.info("全球大学信息爬取完成!")

    def _get_universities_list(self, max_ranking: int) -> List[Dict[str, Any]]:
        """
        获取大学列表

        Args:
            max_ranking: 最大排名

        Returns:
            大学信息列表
        """
        # 从全球大学数据中筛选
        universities = [
            uni for uni in QS_GLOBAL_UNIVERSITIES
            if uni.get('qs_ranking', 999) <= max_ranking
        ]

        # 按排名排序
        universities.sort(key=lambda x: x.get('qs_ranking', 999))

        self.logger.info(f"获取到 {len(universities)} 所排名在 {max_ranking} 以内的大学")
        return universities

    def _scrape_single_university(self, university_info: Dict[str, Any]) -> None:
        """
        爬取单个大学的信息

        Args:
            university_info: 大学基本信息
        """
        # 1. 添加大学信息
        university = self.data_manager.add_university(
            name=university_info['name'],
            country=university_info['country'],
            continent=university_info['continent'],
            qs_ranking=university_info['qs_ranking'],
            website=university_info['website']
        )

        # 2. 爬取Master项目
        try:
            master_programs = self.master_scraper.find_master_programs(university_info['website'])

            if master_programs:
                self._process_master_programs(university.university_id, master_programs)
            else:
                self.logger.warning(f"未找到 {university_info['name']} 的Master项目")

        except Exception as e:
            self.logger.error(f"爬取 {university_info['name']} 的Master项目时出错: {e}")

    def _process_master_programs(self, university_id: int, programs: List[Dict[str, Any]]) -> None:
        """
        处理Master项目信息

        Args:
            university_id: 大学ID
            programs: 项目信息列表
        """
        # 创建默认学院（如果没有具体学院信息）
        default_college = self.data_manager.add_college(
            university_id=university_id,
            college_name="Graduate School"  # 默认研究生院
        )

        for program_info in programs:
            try:
                # 添加专业
                program = self.data_manager.add_program(
                    college_id=default_college.college_id,
                    program_name=program_info.get('program_name', ''),
                    degree=program_info.get('degree', 'Master'),
                    duration=program_info.get('duration'),
                    course_url=program_info.get('course_url'),
                    gpa_requirement=program_info.get('gpa_requirement'),
                    intl_student_fee=program_info.get('intl_student_fee'),
                    language_req=program_info.get('language_req'),
                    intake_months=program_info.get('intake_months'),
                    application_deadline=program_info.get('application_deadline')
                )

                self.stats['programs_found'] += 1

                # 如果有课程信息，也添加进去
                if program_info.get('courses'):
                    for course_info in program_info['courses']:
                        self.data_manager.add_course(
                            program_id=program.program_id,
                            course_name=course_info.get('course_name', ''),
                            course_code=course_info.get('course_code'),
                            course_type=course_info.get('course_type'),
                            credit=course_info.get('credit'),
                            description=course_info.get('description'),
                            course_url=course_info.get('course_url')
                        )
                        self.stats['courses_found'] += 1

            except Exception as e:
                self.logger.error(f"处理项目信息时出错: {e}")

    def _save_all_data(self) -> None:
        """保存所有数据到CSV文件"""
        try:
            # 保存大学数据
            universities_df = pd.DataFrame(self.data_manager.get_universities_data())
            universities_df.to_csv(config.UNIVERSITIES_CSV, index=False, encoding='utf-8')
            self.logger.info(f"大学数据已保存到: {config.UNIVERSITIES_CSV}")

            # 保存学院数据
            colleges_df = pd.DataFrame(self.data_manager.get_colleges_data())
            colleges_csv = config.DATA_DIR + '/colleges.csv'
            colleges_df.to_csv(colleges_csv, index=False, encoding='utf-8')
            self.logger.info(f"学院数据已保存到: {colleges_csv}")

            # 保存专业数据
            programs_df = pd.DataFrame(self.data_manager.get_programs_data())
            programs_df.to_csv(config.PROGRAMS_CSV, index=False, encoding='utf-8')
            self.logger.info(f"专业数据已保存到: {config.PROGRAMS_CSV}")

            # 保存课程数据
            courses_df = pd.DataFrame(self.data_manager.get_courses_data())
            courses_df.to_csv(config.COURSES_CSV, index=False, encoding='utf-8')
            self.logger.info(f"课程数据已保存到: {config.COURSES_CSV}")

        except Exception as e:
            self.logger.error(f"保存数据时出错: {e}")

    def _print_statistics(self) -> None:
        """打印统计信息"""
        print("\n" + "="*60)
        print("爬取统计信息")
        print("="*60)
        print(f"处理的大学数量: {self.stats['universities_processed']}")
        print(f"找到的Master项目数量: {self.stats['programs_found']}")
        print(f"找到的课程数量: {self.stats['courses_found']}")
        print(f"错误数量: {self.stats['errors']}")

        if self.stats['universities_processed'] > 0:
            avg_programs = self.stats['programs_found'] / self.stats['universities_processed']
            print(f"平均每所大学的Master项目数: {avg_programs:.1f}")

        print("="*60)

    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        return self.stats.copy()

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        # 清理资源
        pass


def main():
    """主函数 - 用于测试"""
    import argparse

    parser = argparse.ArgumentParser(description='全球大学Master项目爬虫')
    parser.add_argument('--max-ranking', type=int, default=100, help='最大QS排名')
    parser.add_argument('--limit', type=int, default=5, help='限制爬取数量（测试用）')
    parser.add_argument('--verbose', action='store_true', help='详细输出')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    with GlobalUniversityScraper() as scraper:
        scraper.scrape_global_universities(
            max_ranking=args.max_ranking,
            limit=args.limit
        )


if __name__ == "__main__":
    main()
