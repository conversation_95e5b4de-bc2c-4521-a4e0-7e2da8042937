"""
演示版爬虫 - 生成示例数据而不进行实际网络爬取
用于演示数据结构和CSV导出功能
"""

import pandas as pd
import logging
import random
from datetime import datetime, timedelta
from scrapers.qs_ranking_scraper import QSRankingScraper
from config import UNIVERSITIES_CSV, PROGRAMS_CSV, COURSES_CSV


class DemoScraper:
    """演示版爬虫类 - 生成示例数据"""
    
    def __init__(self):
        """初始化演示爬虫"""
        self.logger = self.setup_logging()
        self.qs_scraper = QSRankingScraper()
        
        # 数据存储
        self.universities_data = []
        self.programs_data = []
        self.courses_data = []
        
        # ID计数器
        self.university_id_counter = 1
        self.program_id_counter = 1
        self.course_id_counter = 1
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def generate_demo_data(self, num_universities: int = 10):
        """
        生成演示数据
        
        Args:
            num_universities: 要生成的大学数量
        """
        self.logger.info(f"开始生成 {num_universities} 所大学的演示数据...")
        
        # 获取QS大学列表
        universities_list = self.qs_scraper.get_qs_us_universities()
        selected_universities = universities_list[:num_universities]
        
        for university_info in selected_universities:
            self.generate_university_data(university_info)
        
        self.save_to_csv()
        self.logger.info("演示数据生成完成!")
    
    def generate_university_data(self, university_info):
        """生成单个大学的数据"""
        university_id = self.university_id_counter
        self.university_id_counter += 1
        
        # 生成大学基本信息
        university_data = {
            'university_id': university_id,
            'name': university_info['name'],
            'country': university_info['country'],
            'continent': university_info['continent'],
            'qs_ranking': university_info['qs_ranking'],
            'website': university_info['website']
        }
        self.universities_data.append(university_data)
        
        # 为每个大学生成2-5个专业
        num_programs = random.randint(2, 5)
        for _ in range(num_programs):
            self.generate_program_data(university_id)
    
    def generate_program_data(self, university_id):
        """生成专业数据"""
        program_id = self.program_id_counter
        self.program_id_counter += 1
        
        # 专业名称池
        program_names = [
            'Computer Science',
            'Business Administration',
            'Electrical Engineering',
            'Mechanical Engineering',
            'Data Science',
            'Artificial Intelligence',
            'Biomedical Engineering',
            'Economics',
            'Psychology',
            'Mathematics',
            'Physics',
            'Chemistry',
            'Biology',
            'Medicine',
            'Law',
            'International Relations',
            'Environmental Science',
            'Materials Science',
            'Aerospace Engineering',
            'Finance'
        ]
        
        program_name = random.choice(program_names)
        
        # 生成专业信息
        program_data = {
            'program_id': program_id,
            'university_id': university_id,
            'name': program_name,
            'related_majors': self.generate_related_majors(program_name),
            'duration': random.choice(['2 years', '4 years', '3 years', '1.5 years']),
            'course_url': f'https://example.edu/programs/{program_name.lower().replace(" ", "-")}',
            'gpa_requirement': round(random.uniform(3.0, 4.0), 1),
            'intl_student_fee': round(random.uniform(30000, 80000), 2),
            'language_req': random.choice([
                'TOEFL 100 / IELTS 7.0',
                'TOEFL 90 / IELTS 6.5',
                'TOEFL 110 / IELTS 7.5',
                'TOEFL 80 / IELTS 6.0'
            ]),
            'intake_months': random.choice(['Fall 2025', 'Spring 2025', 'Fall/Spring 2025']),
            'application_deadline': self.generate_deadline()
        }
        self.programs_data.append(program_data)
        
        # 为每个专业生成3-8门课程
        num_courses = random.randint(3, 8)
        for _ in range(num_courses):
            self.generate_course_data(program_id, program_name)
    
    def generate_related_majors(self, program_name):
        """根据专业名称生成相关专业"""
        related_mapping = {
            'Computer Science': 'Software Engineering,Data Science,AI',
            'Business Administration': 'Management,Finance,Marketing',
            'Electrical Engineering': 'Computer Engineering,Electronics,Telecommunications',
            'Mechanical Engineering': 'Aerospace Engineering,Materials Science,Robotics',
            'Data Science': 'Computer Science,Statistics,Machine Learning',
            'Artificial Intelligence': 'Computer Science,Machine Learning,Robotics',
            'Economics': 'Finance,Business,Statistics',
            'Psychology': 'Cognitive Science,Neuroscience,Behavioral Science',
            'Mathematics': 'Statistics,Physics,Computer Science',
            'Physics': 'Mathematics,Astronomy,Engineering',
            'Chemistry': 'Chemical Engineering,Materials Science,Biology',
            'Biology': 'Biochemistry,Medicine,Environmental Science'
        }
        
        return related_mapping.get(program_name, 'General Studies,Liberal Arts')
    
    def generate_deadline(self):
        """生成申请截止日期"""
        base_date = datetime(2025, 1, 1)
        random_days = random.randint(0, 365)
        deadline = base_date + timedelta(days=random_days)
        return deadline.strftime('%Y-%m-%d')
    
    def generate_course_data(self, program_id, program_name):
        """生成课程数据"""
        course_id = self.course_id_counter
        self.course_id_counter += 1
        
        # 课程名称模板
        course_templates = {
            'Computer Science': [
                'Introduction to Programming',
                'Data Structures and Algorithms',
                'Database Systems',
                'Software Engineering',
                'Computer Networks',
                'Operating Systems',
                'Machine Learning',
                'Artificial Intelligence'
            ],
            'Business Administration': [
                'Principles of Management',
                'Financial Accounting',
                'Marketing Management',
                'Operations Management',
                'Strategic Management',
                'Business Ethics',
                'International Business',
                'Entrepreneurship'
            ],
            'Engineering': [
                'Engineering Mathematics',
                'Engineering Physics',
                'Materials Science',
                'Thermodynamics',
                'Fluid Mechanics',
                'Control Systems',
                'Design Principles',
                'Project Management'
            ]
        }
        
        # 选择课程名称
        if program_name in course_templates:
            course_names = course_templates[program_name]
        elif 'Engineering' in program_name:
            course_names = course_templates['Engineering']
        else:
            course_names = [
                'Introduction to the Field',
                'Research Methods',
                'Advanced Topics',
                'Practical Applications',
                'Theory and Practice',
                'Capstone Project',
                'Seminar',
                'Independent Study'
            ]
        
        course_name = random.choice(course_names)
        
        # 生成课程信息
        course_data = {
            'course_id': course_id,
            'program_id': program_id,
            'course_code': f'{program_name[:3].upper()}{random.randint(100, 599)}',
            'course_name': course_name,
            'course_type': random.choice(['Core', 'Elective', 'Required']),
            'credit': random.choice([3.0, 4.0, 1.5, 2.0, 6.0]),
            'description': f'Comprehensive study of {course_name.lower()} with practical applications',
            'course_url': f'https://example.edu/courses/{course_name.lower().replace(" ", "-")}'
        }
        self.courses_data.append(course_data)
    
    def save_to_csv(self):
        """保存数据到CSV文件"""
        try:
            # 保存大学数据
            if self.universities_data:
                df_universities = pd.DataFrame(self.universities_data)
                df_universities.to_csv(UNIVERSITIES_CSV, index=False, encoding='utf-8')
                self.logger.info(f"大学数据已保存到: {UNIVERSITIES_CSV} ({len(self.universities_data)} 条记录)")
            
            # 保存专业数据
            if self.programs_data:
                df_programs = pd.DataFrame(self.programs_data)
                df_programs.to_csv(PROGRAMS_CSV, index=False, encoding='utf-8')
                self.logger.info(f"专业数据已保存到: {PROGRAMS_CSV} ({len(self.programs_data)} 条记录)")
            
            # 保存课程数据
            if self.courses_data:
                df_courses = pd.DataFrame(self.courses_data)
                df_courses.to_csv(COURSES_CSV, index=False, encoding='utf-8')
                self.logger.info(f"课程数据已保存到: {COURSES_CSV} ({len(self.courses_data)} 条记录)")
                
        except Exception as e:
            self.logger.error(f"保存CSV文件时出错: {e}")
    
    def print_summary(self):
        """打印数据摘要"""
        print("\n" + "="*60)
        print("数据生成摘要")
        print("="*60)
        print(f"大学数量: {len(self.universities_data)}")
        print(f"专业数量: {len(self.programs_data)}")
        print(f"课程数量: {len(self.courses_data)}")
        
        if self.universities_data:
            print(f"\n前5所大学:")
            for i, uni in enumerate(self.universities_data[:5], 1):
                print(f"{i}. {uni['name']} (QS排名: {uni['qs_ranking']})")
        
        if self.programs_data:
            print(f"\n示例专业:")
            for i, prog in enumerate(self.programs_data[:3], 1):
                print(f"{i}. {prog['name']} - 学费: ${prog['intl_student_fee']:,.2f}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='演示版QS大学信息爬虫')
    parser.add_argument('--universities', '-u', type=int, default=10, help='生成的大学数量')
    
    args = parser.parse_args()
    
    scraper = DemoScraper()
    scraper.generate_demo_data(args.universities)
    scraper.print_summary()
    
    print(f"\n✅ 演示数据已生成并保存到CSV文件!")
    print(f"📁 查看 data/ 目录下的文件:")
    print(f"   - universities.csv")
    print(f"   - programs.csv") 
    print(f"   - courses.csv")


if __name__ == "__main__":
    main()
