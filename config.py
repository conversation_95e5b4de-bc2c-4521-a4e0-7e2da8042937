"""
配置文件 - 爬虫相关配置
"""

import os

# 基础配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, 'data')

# 确保数据目录存在
os.makedirs(DATA_DIR, exist_ok=True)

# 输出文件路径
UNIVERSITIES_CSV = os.path.join(DATA_DIR, 'universities.csv')
PROGRAMS_CSV = os.path.join(DATA_DIR, 'programs.csv')
COURSES_CSV = os.path.join(DATA_DIR, 'courses.csv')

# 爬虫配置
REQUEST_DELAY = (1, 3)  # 请求间隔范围（秒）
TIMEOUT = 30  # 请求超时时间
MAX_RETRIES = 3  # 最大重试次数

# User-Agent列表
USER_AGENTS = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
]

# QS前500美国大学列表将从QS排名爬虫动态获取
US_UNIVERSITIES_QS_TOP500 = None  # 将在运行时从QS排名爬虫获取

# Selenium配置
SELENIUM_CONFIG = {
    'headless': True,
    'window_size': (1920, 1080),
    'page_load_timeout': 30,
    'implicit_wait': 10
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'filename': os.path.join(DATA_DIR, 'scraper.log')
}
