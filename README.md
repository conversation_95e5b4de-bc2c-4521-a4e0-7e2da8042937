# QS前500美国大学信息爬虫

这是一个用于爬取QS前500美国高校信息的Python程序，可以获取大学基本信息、专业信息和课程信息，并保存到CSV文件中。

## 功能特性

- 🎓 爬取QS前500美国大学的基本信息
- 📚 获取各大学的专业/学位项目信息
- 📖 收集专业相关的课程信息
- 💾 数据导出为CSV格式，便于后续分析
- 🔄 支持断点续传和错误重试
- 📊 详细的日志记录

## 数据结构

### 1. 大学信息 (universities.csv)
- university_id: 大学唯一ID
- name: 大学名称
- country: 国家
- continent: 所在洲
- qs_ranking: QS排名
- website: 大学官网URL

### 2. 专业信息 (programs.csv)
- program_id: 专业唯一ID
- university_id: 所属大学ID
- name: 专业名称
- related_majors: 相关专业
- duration: 学制
- course_url: 专业课程链接
- gpa_requirement: 申请最低GPA要求
- intl_student_fee: 国际学生学费
- language_req: 语言要求
- intake_months: 入学时间
- application_deadline: 申请截止日期

### 3. 课程信息 (courses.csv)
- course_id: 课程唯一ID
- program_id: 所属专业ID
- course_code: 课程编号
- course_name: 课程名称
- course_type: 类型（必修/选修）
- credit: 学分数
- description: 课程描述
- course_url: 课程介绍链接

## 安装依赖

```bash
# 使用pip安装依赖
pip install requests beautifulsoup4 selenium pandas lxml

# 或者使用poetry（推荐）
poetry install
```

## 使用方法

### 基本使用

```bash
# 爬取所有QS前500美国大学
python main.py

# 详细输出模式
python main.py --verbose

# 限制爬取数量（用于测试）
python main.py --limit 10
```

### 程序化使用

```python
from scrapers.university_scraper import UniversityScraper

# 使用上下文管理器
with UniversityScraper() as scraper:
    scraper.scrape_all_universities(max_ranking=100)  # 只爬取QS前100
```

### 单独获取QS排名数据

```python
from scrapers.qs_ranking_scraper import QSRankingScraper

scraper = QSRankingScraper()
universities = scraper.get_qs_us_universities()
top_50 = scraper.filter_by_ranking(50)
```

## 配置说明

主要配置在 `config.py` 文件中：

- `REQUEST_DELAY`: 请求间隔时间（避免被反爬虫）
- `TIMEOUT`: 请求超时时间
- `MAX_RETRIES`: 最大重试次数
- `USER_AGENTS`: User-Agent列表
- `SELENIUM_CONFIG`: Selenium浏览器配置

## 输出文件

程序运行后会在 `data/` 目录下生成以下文件：

- `universities.csv`: 大学基本信息
- `programs.csv`: 专业信息
- `courses.csv`: 课程信息
- `scraper.log`: 运行日志
- `qs_us_universities.json`: QS美国大学列表缓存

## 注意事项

1. **请求频率**: 程序内置了请求延迟机制，请勿修改过小以免被网站封禁
2. **网络环境**: 建议在稳定的网络环境下运行
3. **Chrome驱动**: 确保项目目录下有正确版本的 `chromedriver`
4. **数据准确性**: 爬取的数据可能因网站更新而有所变化，建议定期更新
5. **合规使用**: 请遵守各大学网站的robots.txt和使用条款

## 故障排除

### 常见问题

1. **ChromeDriver错误**
   ```bash
   # 下载对应版本的ChromeDriver
   # 放置在项目根目录下
   ```

2. **网络连接超时**
   ```python
   # 在config.py中增加超时时间
   TIMEOUT = 60
   ```

3. **内存不足**
   ```python
   # 减少并发数量或分批处理
   python main.py --limit 50
   ```

## 扩展功能

### 添加新的大学

在 `scrapers/qs_ranking_scraper.py` 中的大学列表中添加新的大学信息。

### 自定义数据提取

修改 `scrapers/university_scraper.py` 中的提取逻辑来获取更多字段。

### 数据后处理

```python
import pandas as pd

# 读取生成的CSV文件
universities = pd.read_csv('data/universities.csv')
programs = pd.read_csv('data/programs.csv')
courses = pd.read_csv('data/courses.csv')

# 进行数据分析...
```

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。