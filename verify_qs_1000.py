"""
验证QS前1000大学数据的脚本
"""

import json
import sys
import os

def main():
    """主函数"""
    print("验证QS前1000大学数据...")
    
    try:
        # 加载JSON文件
        json_file = 'data/qs_global_universities.json'
        with open(json_file, 'r', encoding='utf-8') as f:
            universities = json.load(f)
        
        print(f"✅ 成功加载 {len(universities)} 所大学")
        
        # 验证排名连续性
        rankings = [uni['qs_ranking'] for uni in universities]
        expected_rankings = list(range(1, 1001))
        
        if rankings == expected_rankings:
            print("✅ 排名连续性检查通过 (1-1000)")
        else:
            print("❌ 排名连续性检查失败")
            missing = set(expected_rankings) - set(rankings)
            if missing:
                print(f"缺失排名: {sorted(list(missing))[:10]}...")
        
        # 统计各洲分布
        continent_stats = {}
        for uni in universities:
            continent = uni['continent']
            continent_stats[continent] = continent_stats.get(continent, 0) + 1
        
        print("\n各洲分布:")
        for continent, count in sorted(continent_stats.items()):
            print(f"  {continent}: {count} 所大学")
        
        # 统计各国分布（前10）
        country_stats = {}
        for uni in universities:
            country = uni['country']
            country_stats[country] = country_stats.get(country, 0) + 1
        
        print("\n主要国家分布 (前10):")
        sorted_countries = sorted(country_stats.items(), key=lambda x: x[1], reverse=True)[:10]
        for country, count in sorted_countries:
            print(f"  {country}: {count} 所大学")
        
        # 显示各洲示例大学
        print("\n各洲示例大学:")
        continents_shown = set()
        for uni in universities:
            continent = uni['continent']
            if continent not in continents_shown:
                print(f"  {continent}: {uni['name']} (QS排名: {uni['qs_ranking']}, {uni['country']})")
                continents_shown.add(continent)
                if len(continents_shown) >= 6:
                    break
        
        print(f"\n✅ QS前1000大学数据验证完成!")
        print(f"数据文件: {json_file}")
        print(f"文件大小: {os.path.getsize(json_file) / 1024:.1f} KB")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {json_file}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
