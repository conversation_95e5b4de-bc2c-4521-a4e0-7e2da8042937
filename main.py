"""
主程序 - QS前1000全球大学Master项目信息爬虫
"""

import sys
import argparse
import logging
from scrapers.global_university_scraper import GlobalUniversityScraper
from scrapers.utils import setup_logging


def main():
    """主函数"""
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='QS前1000全球大学Master项目信息爬虫')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--limit', '-l', type=int, default=None, help='限制爬取的大学数量')
    parser.add_argument('--max-ranking', '-r', type=int, default=1000, help='最大QS排名限制')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    logger = setup_logging()
    logger.info(f"开始执行QS前{args.max_ranking}全球大学Master项目信息爬虫...")

    try:
        with GlobalUniversityScraper() as scraper:
            scraper.scrape_global_universities(
                max_ranking=args.max_ranking,
                limit=args.limit
            )

        logger.info("爬虫执行完成!")

    except KeyboardInterrupt:
        logger.info("用户中断了程序执行")
        sys.exit(1)

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
