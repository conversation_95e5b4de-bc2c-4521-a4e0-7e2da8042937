"""
主程序 - QS前500美国大学信息爬虫
"""

import sys
import argparse
import logging
from scrapers.university_scraper import UniversityScraper
from scrapers.utils import setup_logging


def main():
    """主函数"""
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='QS前500美国大学信息爬虫')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--limit', '-l', type=int, default=None, help='限制爬取的大学数量')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    logger = setup_logging()
    logger.info("开始执行QS前500美国大学信息爬虫...")

    try:
        with UniversityScraper() as scraper:
            # 设置排名限制
            max_ranking = 500
            if args.limit:
                # 如果设置了数量限制，我们需要在爬取后限制
                logger.info(f"将限制爬取数量为: {args.limit}")

            scraper.scrape_all_universities(max_ranking=max_ranking)

        logger.info("爬虫执行完成!")

    except KeyboardInterrupt:
        logger.info("用户中断了程序执行")
        sys.exit(1)

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
