course_id,program_id,course_code,course_name,course_type,credit,description,course_url
1,1,CS500,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
2,1,CS501,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
3,1,CS502,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
4,1,CS503,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
5,1,CS504,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
6,1,CS505,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
7,1,CS506,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
8,1,CS507,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
9,1,CS508,Data Structures,Elective,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
10,1,CS509,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
11,2,CS500,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
12,2,CS501,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
13,2,CS502,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
14,2,CS503,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
15,2,CS504,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
16,2,CS505,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
17,2,CS506,Advanced Algorithms,Elective,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
18,2,CS507,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
19,2,CS508,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
20,2,CS509,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
21,2,CS510,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
22,2,CS511,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
23,2,CS512,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
24,2,CS513,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
25,2,CS514,Computer Networks,Elective,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
26,3,CS500,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
27,3,CS501,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
28,3,CS502,Computer Graphics,Core,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
29,3,CS503,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
30,3,CS504,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
31,3,CS505,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
32,3,CS506,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
33,3,CS507,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
34,3,CS508,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
35,3,CS509,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
36,3,CS510,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
37,3,CS511,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
38,3,CS512,Cybersecurity,Core,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
39,3,CS513,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
40,4,CS500,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
41,4,CS501,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
42,4,CS502,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
43,4,CS503,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
44,4,CS504,Advanced Mathematics,Core,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
45,4,CS505,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
46,4,CS506,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
47,4,CS507,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
48,4,CS508,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
49,4,CS509,Database Systems,Required,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
50,4,CS510,Statistics for Engineers,Required,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
51,5,CS500,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
52,5,CS501,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
53,5,CS502,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
54,5,CS503,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
55,5,CS504,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
56,5,CS505,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
57,5,CS506,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
58,5,CS507,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
59,6,CS500,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
60,6,CS501,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
61,6,CS502,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
62,6,CS503,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
63,6,CS504,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
64,6,CS505,Computer Networks,Elective,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
65,6,CS506,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
66,6,CS507,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
67,6,CS508,Operating Systems,Core,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
68,6,CS509,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
69,6,CS510,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
70,6,CS511,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
71,6,CS512,Thesis Preparation,Required,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
72,6,CS513,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
73,7,CS500,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
74,7,CS501,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
75,7,CS502,Software Engineering,Elective,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
76,7,CS503,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
77,7,CS504,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
78,7,CS505,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
79,7,CS506,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
80,7,CS507,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
81,7,CS508,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
82,7,CS509,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
83,7,CS510,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
84,8,CS500,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
85,8,CS501,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
86,8,CS502,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
87,8,CS503,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
88,8,CS504,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
89,8,CS505,Data Structures,Core,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
90,8,CS506,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
91,8,CS507,Machine Learning,Required,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
92,8,CS508,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
93,8,CS509,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
94,8,CS510,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
95,8,CS511,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
96,8,CS512,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
97,8,CS513,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
98,9,CS500,Thesis Preparation,Elective,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
99,9,CS501,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
100,9,CS502,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
101,9,CS503,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
102,9,CS504,Statistics for Engineers,Required,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
103,9,CS505,Machine Learning,Required,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
104,9,CS506,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
105,9,CS507,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
106,9,CS508,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
107,9,CS509,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
108,9,CS510,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
109,9,CS511,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
110,10,CS500,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
111,10,CS501,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
112,10,CS502,Machine Learning,Required,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
113,10,CS503,Data Structures,Core,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
114,10,CS504,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
115,10,CS505,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
116,10,CS506,Statistics for Engineers,Required,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
117,10,CS507,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
118,10,CS508,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
119,10,CS509,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
120,10,CS510,Operating Systems,Required,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
121,10,CS511,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
122,10,CS512,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
123,10,CS513,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
124,10,CS514,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
125,11,CS500,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
126,11,CS501,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
127,11,CS502,Thesis Preparation,Elective,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
128,11,CS503,Computer Graphics,Required,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
129,11,CS504,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
130,11,CS505,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
131,11,CS506,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
132,11,CS507,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
133,11,CS508,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
134,12,CS500,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
135,12,CS501,Computer Graphics,Core,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
136,12,CS502,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
137,12,CS503,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
138,12,CS504,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
139,12,CS505,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
140,12,CS506,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
141,12,CS507,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
142,12,CS508,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
143,12,CS509,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
144,12,CS510,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
145,12,CS511,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
146,12,CS512,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
147,12,CS513,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
148,13,CS500,Operating Systems,Core,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
149,13,CS501,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
150,13,CS502,Advanced Mathematics,Required,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
151,13,CS503,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
152,13,CS504,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
153,13,CS505,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
154,13,CS506,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
155,13,CS507,Database Systems,Required,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
156,13,CS508,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
157,14,CS500,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
158,14,CS501,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
159,14,CS502,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
160,14,CS503,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
161,14,CS504,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
162,14,CS505,Operating Systems,Core,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
163,14,CS506,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
164,14,CS507,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
165,15,CS500,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
166,15,CS501,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
167,15,CS502,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
168,15,CS503,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
169,15,CS504,Advanced Algorithms,Required,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
170,15,CS505,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
171,15,CS506,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
172,15,CS507,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
173,15,CS508,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
174,15,CS509,Project Management,Elective,6.0,Advanced course in project management,https://example.edu/courses/project-management
175,15,CS510,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
176,15,CS511,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
177,15,CS512,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
178,15,CS513,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
179,16,CS500,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
180,16,CS501,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
181,16,CS502,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
182,16,CS503,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
183,16,CS504,Statistics for Engineers,Required,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
184,16,CS505,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
185,16,CS506,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
186,16,CS507,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
187,16,CS508,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
188,16,CS509,Artificial Intelligence,Elective,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
189,16,CS510,Cybersecurity,Elective,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
190,16,CS511,Machine Learning,Required,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
191,16,CS512,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
192,16,CS513,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
193,16,CS514,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
194,17,CS500,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
195,17,CS501,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
196,17,CS502,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
197,17,CS503,Machine Learning,Required,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
198,17,CS504,Advanced Algorithms,Elective,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
199,17,CS505,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
200,17,CS506,Project Management,Elective,6.0,Advanced course in project management,https://example.edu/courses/project-management
201,17,CS507,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
202,17,CS508,Thesis Preparation,Core,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
203,17,CS509,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
204,17,CS510,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
205,17,CS511,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
206,17,CS512,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
207,17,CS513,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
208,17,CS514,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
209,18,CS500,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
210,18,CS501,Software Engineering,Elective,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
211,18,CS502,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
212,18,CS503,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
213,18,CS504,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
214,18,CS505,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
215,18,CS506,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
216,18,CS507,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
217,18,CS508,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
218,18,CS509,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
219,18,CS510,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
220,18,CS511,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
221,18,CS512,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
222,18,CS513,Cybersecurity,Core,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
223,18,CS514,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
224,19,CS500,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
225,19,CS501,Advanced Mathematics,Core,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
226,19,CS502,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
227,19,CS503,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
228,19,CS504,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
229,19,CS505,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
230,19,CS506,Software Engineering,Elective,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
231,19,CS507,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
232,19,CS508,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
233,19,CS509,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
234,19,CS510,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
235,19,CS511,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
236,20,CS500,Operating Systems,Required,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
237,20,CS501,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
238,20,CS502,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
239,20,CS503,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
240,20,CS504,Database Systems,Required,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
241,20,CS505,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
242,20,CS506,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
243,20,CS507,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
244,20,CS508,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
245,20,CS509,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
246,20,CS510,Data Structures,Elective,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
247,20,CS511,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
248,20,CS512,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
249,21,CS500,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
250,21,CS501,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
251,21,CS502,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
252,21,CS503,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
253,21,CS504,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
254,21,CS505,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
255,21,CS506,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
256,21,CS507,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
257,21,CS508,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
258,21,CS509,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
259,22,CS500,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
260,22,CS501,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
261,22,CS502,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
262,22,CS503,Advanced Mathematics,Core,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
263,22,CS504,Advanced Algorithms,Elective,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
264,22,CS505,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
265,22,CS506,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
266,22,CS507,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
267,22,CS508,Project Management,Core,6.0,Advanced course in project management,https://example.edu/courses/project-management
268,22,CS509,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
269,22,CS510,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
270,23,CS500,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
271,23,CS501,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
272,23,CS502,Advanced Algorithms,Elective,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
273,23,CS503,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
274,23,CS504,Project Management,Core,6.0,Advanced course in project management,https://example.edu/courses/project-management
275,23,CS505,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
276,23,CS506,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
277,23,CS507,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
278,23,CS508,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
279,23,CS509,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
280,23,CS510,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
281,23,CS511,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
282,23,CS512,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
283,24,CS500,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
284,24,CS501,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
285,24,CS502,Computer Networks,Elective,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
286,24,CS503,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
287,24,CS504,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
288,24,CS505,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
289,24,CS506,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
290,24,CS507,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
291,24,CS508,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
292,24,CS509,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
293,24,CS510,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
294,24,CS511,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
295,24,CS512,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
296,24,CS513,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
297,25,CS500,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
298,25,CS501,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
299,25,CS502,Cybersecurity,Elective,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
300,25,CS503,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
301,25,CS504,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
302,25,CS505,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
303,25,CS506,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
304,25,CS507,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
305,25,CS508,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
306,25,CS509,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
307,25,CS510,Thesis Preparation,Core,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
308,25,CS511,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
309,25,CS512,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
310,26,CS500,Operating Systems,Required,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
311,26,CS501,Software Engineering,Elective,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
312,26,CS502,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
313,26,CS503,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
314,26,CS504,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
315,26,CS505,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
316,26,CS506,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
317,26,CS507,Project Management,Core,6.0,Advanced course in project management,https://example.edu/courses/project-management
318,26,CS508,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
319,26,CS509,Data Structures,Elective,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
320,26,CS510,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
321,26,CS511,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
322,26,CS512,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
323,27,CS500,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
324,27,CS501,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
325,27,CS502,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
326,27,CS503,Operating Systems,Core,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
327,27,CS504,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
328,27,CS505,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
329,27,CS506,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
330,27,CS507,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
331,28,CS500,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
332,28,CS501,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
333,28,CS502,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
334,28,CS503,Cybersecurity,Core,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
335,28,CS504,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
336,28,CS505,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
337,28,CS506,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
338,28,CS507,Database Systems,Elective,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
339,28,CS508,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
340,28,CS509,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
341,28,CS510,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
342,28,CS511,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
343,28,CS512,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
344,29,CS500,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
345,29,CS501,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
346,29,CS502,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
347,29,CS503,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
348,29,CS504,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
349,29,CS505,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
350,29,CS506,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
351,29,CS507,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
352,29,CS508,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
353,30,CS500,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
354,30,CS501,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
355,30,CS502,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
356,30,CS503,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
357,30,CS504,Computer Networks,Elective,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
358,30,CS505,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
359,30,CS506,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
360,30,CS507,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
361,31,CS500,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
362,31,CS501,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
363,31,CS502,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
364,31,CS503,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
365,31,CS504,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
366,31,CS505,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
367,31,CS506,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
368,31,CS507,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
369,31,CS508,Cybersecurity,Elective,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
370,31,CS509,Advanced Algorithms,Required,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
371,31,CS510,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
372,31,CS511,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
373,31,CS512,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
374,32,CS500,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
375,32,CS501,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
376,32,CS502,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
377,32,CS503,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
378,32,CS504,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
379,32,CS505,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
380,32,CS506,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
381,32,CS507,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
382,32,CS508,Operating Systems,Required,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
383,32,CS509,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
384,33,CS500,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
385,33,CS501,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
386,33,CS502,Cybersecurity,Elective,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
387,33,CS503,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
388,33,CS504,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
389,33,CS505,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
390,33,CS506,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
391,33,CS507,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
392,33,CS508,Advanced Mathematics,Core,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
393,33,CS509,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
394,34,CS500,Operating Systems,Required,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
395,34,CS501,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
396,34,CS502,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
397,34,CS503,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
398,34,CS504,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
399,34,CS505,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
400,34,CS506,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
401,34,CS507,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
402,34,CS508,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
403,34,CS509,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
404,34,CS510,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
405,34,CS511,Software Engineering,Elective,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
406,34,CS512,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
407,34,CS513,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
408,34,CS514,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
409,35,CS500,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
410,35,CS501,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
411,35,CS502,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
412,35,CS503,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
413,35,CS504,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
414,35,CS505,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
415,35,CS506,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
416,35,CS507,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
417,35,CS508,Advanced Mathematics,Required,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
418,36,CS500,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
419,36,CS501,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
420,36,CS502,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
421,36,CS503,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
422,36,CS504,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
423,36,CS505,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
424,36,CS506,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
425,36,CS507,Cybersecurity,Required,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
426,37,CS500,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
427,37,CS501,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
428,37,CS502,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
429,37,CS503,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
430,37,CS504,Computer Networks,Elective,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
431,37,CS505,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
432,37,CS506,Advanced Mathematics,Required,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
433,37,CS507,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
434,37,CS508,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
435,37,CS509,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
436,37,CS510,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
437,37,CS511,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
438,37,CS512,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
439,37,CS513,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
440,38,CS500,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
441,38,CS501,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
442,38,CS502,Computer Networks,Elective,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
443,38,CS503,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
444,38,CS504,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
445,38,CS505,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
446,38,CS506,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
447,38,CS507,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
448,38,CS508,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
449,38,CS509,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
450,39,CS500,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
451,39,CS501,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
452,39,CS502,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
453,39,CS503,Thesis Preparation,Required,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
454,39,CS504,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
455,39,CS505,Artificial Intelligence,Elective,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
456,39,CS506,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
457,39,CS507,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
458,39,CS508,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
459,39,CS509,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
460,39,CS510,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
461,39,CS511,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
462,39,CS512,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
463,39,CS513,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
464,40,CS500,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
465,40,CS501,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
466,40,CS502,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
467,40,CS503,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
468,40,CS504,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
469,40,CS505,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
470,40,CS506,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
471,40,CS507,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
472,41,CS500,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
473,41,CS501,Statistics for Engineers,Core,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
474,41,CS502,Operating Systems,Required,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
475,41,CS503,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
476,41,CS504,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
477,41,CS505,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
478,41,CS506,Computer Networks,Elective,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
479,41,CS507,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
480,41,CS508,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
481,41,CS509,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
482,42,CS500,Data Structures,Elective,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
483,42,CS501,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
484,42,CS502,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
485,42,CS503,Machine Learning,Required,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
486,42,CS504,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
487,42,CS505,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
488,42,CS506,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
489,42,CS507,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
490,42,CS508,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
491,42,CS509,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
492,42,CS510,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
493,42,CS511,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
494,43,CS500,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
495,43,CS501,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
496,43,CS502,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
497,43,CS503,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
498,43,CS504,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
499,43,CS505,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
500,43,CS506,Cybersecurity,Core,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
501,43,CS507,Data Structures,Elective,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
502,43,CS508,Computer Graphics,Core,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
503,43,CS509,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
504,43,CS510,Software Engineering,Elective,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
505,44,CS500,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
506,44,CS501,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
507,44,CS502,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
508,44,CS503,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
509,44,CS504,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
510,44,CS505,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
511,44,CS506,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
512,44,CS507,Database Systems,Required,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
513,44,CS508,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
514,44,CS509,Software Engineering,Elective,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
515,44,CS510,Computer Graphics,Core,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
516,45,CS500,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
517,45,CS501,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
518,45,CS502,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
519,45,CS503,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
520,45,CS504,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
521,45,CS505,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
522,45,CS506,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
523,45,CS507,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
524,45,CS508,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
525,45,CS509,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
526,45,CS510,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
527,45,CS511,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
528,45,CS512,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
529,45,CS513,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
530,45,CS514,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
531,46,CS500,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
532,46,CS501,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
533,46,CS502,Data Structures,Core,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
534,46,CS503,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
535,46,CS504,Machine Learning,Required,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
536,46,CS505,Advanced Mathematics,Core,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
537,46,CS506,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
538,46,CS507,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
539,46,CS508,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
540,46,CS509,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
541,47,CS500,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
542,47,CS501,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
543,47,CS502,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
544,47,CS503,Database Systems,Required,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
545,47,CS504,Statistics for Engineers,Required,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
546,47,CS505,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
547,47,CS506,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
548,47,CS507,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
549,47,CS508,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
550,47,CS509,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
551,47,CS510,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
552,47,CS511,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
553,47,CS512,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
554,48,CS500,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
555,48,CS501,Database Systems,Elective,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
556,48,CS502,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
557,48,CS503,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
558,48,CS504,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
559,48,CS505,Computer Graphics,Core,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
560,48,CS506,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
561,48,CS507,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
562,48,CS508,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
563,48,CS509,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
564,49,CS500,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
565,49,CS501,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
566,49,CS502,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
567,49,CS503,Computer Networks,Elective,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
568,49,CS504,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
569,49,CS505,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
570,49,CS506,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
571,49,CS507,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
572,49,CS508,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
573,49,CS509,Operating Systems,Core,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
574,49,CS510,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
575,49,CS511,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
576,49,CS512,Project Management,Elective,6.0,Advanced course in project management,https://example.edu/courses/project-management
577,49,CS513,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
578,49,CS514,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
579,50,CS500,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
580,50,CS501,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
581,50,CS502,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
582,50,CS503,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
583,50,CS504,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
584,50,CS505,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
585,50,CS506,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
586,50,CS507,Cybersecurity,Core,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
587,50,CS508,Computer Networks,Elective,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
588,50,CS509,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
589,50,CS510,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
590,50,CS511,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
591,50,CS512,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
592,50,CS513,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
593,50,CS514,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
594,51,CS500,Data Structures,Elective,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
595,51,CS501,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
596,51,CS502,Machine Learning,Required,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
597,51,CS503,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
598,51,CS504,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
599,51,CS505,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
600,51,CS506,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
601,51,CS507,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
602,52,CS500,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
603,52,CS501,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
604,52,CS502,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
605,52,CS503,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
606,52,CS504,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
607,52,CS505,Statistics for Engineers,Core,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
608,52,CS506,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
609,52,CS507,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
610,52,CS508,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
