course_id,program_id,course_code,course_name,course_type,credit,description,course_url
1,1,CS500,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
2,1,CS501,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
3,1,CS502,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
4,1,CS503,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
5,1,CS504,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
6,1,CS505,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
7,1,CS506,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
8,1,CS507,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
9,1,CS508,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
10,1,CS509,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
11,1,CS510,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
12,1,CS511,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
13,1,CS512,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
14,1,CS513,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
15,1,CS514,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
16,2,CS500,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
17,2,CS501,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
18,2,CS502,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
19,2,CS503,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
20,2,CS504,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
21,2,CS505,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
22,2,CS506,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
23,2,CS507,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
24,3,CS500,Operating Systems,Core,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
25,3,CS501,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
26,3,CS502,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
27,3,CS503,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
28,3,CS504,Data Structures,Core,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
29,3,CS505,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
30,3,CS506,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
31,3,CS507,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
32,3,CS508,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
33,3,CS509,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
34,3,CS510,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
35,3,CS511,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
36,3,CS512,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
37,3,CS513,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
38,3,CS514,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
39,4,CS500,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
40,4,CS501,Cybersecurity,Elective,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
41,4,CS502,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
42,4,CS503,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
43,4,CS504,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
44,4,CS505,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
45,4,CS506,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
46,4,CS507,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
47,4,CS508,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
48,4,CS509,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
49,4,CS510,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
50,4,CS511,Software Engineering,Elective,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
51,4,CS512,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
52,4,CS513,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
53,4,CS514,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
54,5,CS500,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
55,5,CS501,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
56,5,CS502,Database Systems,Required,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
57,5,CS503,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
58,5,CS504,Cybersecurity,Elective,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
59,5,CS505,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
60,5,CS506,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
61,5,CS507,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
62,5,CS508,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
63,5,CS509,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
64,5,CS510,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
65,6,CS500,Data Structures,Elective,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
66,6,CS501,Advanced Mathematics,Core,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
67,6,CS502,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
68,6,CS503,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
69,6,CS504,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
70,6,CS505,Thesis Preparation,Core,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
71,6,CS506,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
72,6,CS507,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
73,6,CS508,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
74,6,CS509,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
75,7,CS500,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
76,7,CS501,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
77,7,CS502,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
78,7,CS503,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
79,7,CS504,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
80,7,CS505,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
81,7,CS506,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
82,7,CS507,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
83,7,CS508,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
84,7,CS509,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
85,8,CS500,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
86,8,CS501,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
87,8,CS502,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
88,8,CS503,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
89,8,CS504,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
90,8,CS505,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
91,8,CS506,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
92,8,CS507,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
93,8,CS508,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
94,8,CS509,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
95,8,CS510,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
96,8,CS511,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
97,9,CS500,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
98,9,CS501,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
99,9,CS502,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
100,9,CS503,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
101,9,CS504,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
102,9,CS505,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
103,9,CS506,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
104,9,CS507,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
105,10,CS500,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
106,10,CS501,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
107,10,CS502,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
108,10,CS503,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
109,10,CS504,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
110,10,CS505,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
111,10,CS506,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
112,10,CS507,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
113,10,CS508,Project Management,Elective,6.0,Advanced course in project management,https://example.edu/courses/project-management
114,10,CS509,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
115,10,CS510,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
116,11,CS500,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
117,11,CS501,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
118,11,CS502,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
119,11,CS503,Statistics for Engineers,Required,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
120,11,CS504,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
121,11,CS505,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
122,11,CS506,Database Systems,Elective,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
123,11,CS507,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
124,11,CS508,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
125,11,CS509,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
126,11,CS510,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
127,12,CS500,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
128,12,CS501,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
129,12,CS502,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
130,12,CS503,Thesis Preparation,Required,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
131,12,CS504,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
132,12,CS505,Cybersecurity,Required,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
133,12,CS506,Operating Systems,Core,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
134,12,CS507,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
135,12,CS508,Advanced Algorithms,Required,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
136,12,CS509,Computer Networks,Elective,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
137,13,CS500,Advanced Algorithms,Required,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
138,13,CS501,Software Engineering,Elective,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
139,13,CS502,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
140,13,CS503,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
141,13,CS504,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
142,13,CS505,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
143,13,CS506,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
144,13,CS507,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
145,13,CS508,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
146,13,CS509,Database Systems,Elective,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
147,14,CS500,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
148,14,CS501,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
149,14,CS502,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
150,14,CS503,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
151,14,CS504,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
152,14,CS505,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
153,14,CS506,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
154,14,CS507,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
155,14,CS508,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
156,14,CS509,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
157,15,CS500,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
158,15,CS501,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
159,15,CS502,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
160,15,CS503,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
161,15,CS504,Machine Learning,Required,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
162,15,CS505,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
163,15,CS506,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
164,15,CS507,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
165,15,CS508,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
166,15,CS509,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
167,16,CS500,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
168,16,CS501,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
169,16,CS502,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
170,16,CS503,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
171,16,CS504,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
172,16,CS505,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
173,16,CS506,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
174,16,CS507,Advanced Algorithms,Elective,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
175,16,CS508,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
176,16,CS509,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
177,16,CS510,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
178,16,CS511,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
179,16,CS512,Cybersecurity,Core,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
180,16,CS513,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
181,17,CS500,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
182,17,CS501,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
183,17,CS502,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
184,17,CS503,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
185,17,CS504,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
186,17,CS505,Advanced Mathematics,Core,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
187,17,CS506,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
188,17,CS507,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
189,17,CS508,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
190,17,CS509,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
191,17,CS510,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
192,17,CS511,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
193,18,CS500,Cybersecurity,Core,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
194,18,CS501,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
195,18,CS502,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
196,18,CS503,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
197,18,CS504,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
198,18,CS505,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
199,18,CS506,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
200,18,CS507,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
201,18,CS508,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
202,18,CS509,Data Structures,Core,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
203,18,CS510,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
204,18,CS511,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
205,18,CS512,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
206,19,CS500,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
207,19,CS501,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
208,19,CS502,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
209,19,CS503,Machine Learning,Required,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
210,19,CS504,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
211,19,CS505,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
212,19,CS506,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
213,19,CS507,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
214,19,CS508,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
215,19,CS509,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
216,19,CS510,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
217,19,CS511,Computer Networks,Elective,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
218,19,CS512,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
219,20,CS500,Thesis Preparation,Core,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
220,20,CS501,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
221,20,CS502,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
222,20,CS503,Computer Networks,Elective,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
223,20,CS504,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
224,20,CS505,Software Engineering,Elective,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
225,20,CS506,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
226,20,CS507,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
227,20,CS508,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
228,20,CS509,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
229,20,CS510,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
230,20,CS511,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
231,20,CS512,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
232,21,CS500,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
233,21,CS501,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
234,21,CS502,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
235,21,CS503,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
236,21,CS504,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
237,21,CS505,Cybersecurity,Required,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
238,21,CS506,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
239,21,CS507,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
240,21,CS508,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
241,21,CS509,Statistics for Engineers,Core,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
242,21,CS510,Operating Systems,Core,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
243,21,CS511,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
244,21,CS512,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
245,22,CS500,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
246,22,CS501,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
247,22,CS502,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
248,22,CS503,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
249,22,CS504,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
250,22,CS505,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
251,22,CS506,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
252,22,CS507,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
253,22,CS508,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
254,23,CS500,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
255,23,CS501,Project Management,Core,6.0,Advanced course in project management,https://example.edu/courses/project-management
256,23,CS502,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
257,23,CS503,Data Structures,Elective,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
258,23,CS504,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
259,23,CS505,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
260,23,CS506,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
261,23,CS507,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
262,23,CS508,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
263,23,CS509,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
264,23,CS510,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
265,24,CS500,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
266,24,CS501,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
267,24,CS502,Advanced Mathematics,Required,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
268,24,CS503,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
269,24,CS504,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
270,24,CS505,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
271,24,CS506,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
272,24,CS507,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
273,25,CS500,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
274,25,CS501,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
275,25,CS502,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
276,25,CS503,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
277,25,CS504,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
278,25,CS505,Statistics for Engineers,Elective,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
279,25,CS506,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
280,25,CS507,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
281,25,CS508,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
282,25,CS509,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
283,25,CS510,Cybersecurity,Elective,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
284,25,CS511,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
285,25,CS512,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
286,26,CS500,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
287,26,CS501,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
288,26,CS502,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
289,26,CS503,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
290,26,CS504,Machine Learning,Required,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
291,26,CS505,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
292,26,CS506,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
293,26,CS507,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
294,26,CS508,Advanced Mathematics,Required,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
295,26,CS509,Project Management,Elective,6.0,Advanced course in project management,https://example.edu/courses/project-management
296,26,CS510,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
297,26,CS511,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
298,26,CS512,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
299,27,CS500,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
300,27,CS501,Operating Systems,Core,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
301,27,CS502,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
302,27,CS503,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
303,27,CS504,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
304,27,CS505,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
305,27,CS506,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
306,27,CS507,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
307,27,CS508,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
308,27,CS509,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
309,27,CS510,Data Structures,Elective,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
310,27,CS511,Project Management,Elective,4.0,Advanced course in project management,https://example.edu/courses/project-management
311,28,CS500,Thesis Preparation,Required,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
312,28,CS501,Project Management,Core,6.0,Advanced course in project management,https://example.edu/courses/project-management
313,28,CS502,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
314,28,CS503,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
315,28,CS504,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
316,28,CS505,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
317,28,CS506,Machine Learning,Required,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
318,28,CS507,Data Structures,Core,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
319,28,CS508,Advanced Algorithms,Elective,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
320,28,CS509,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
321,28,CS510,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
322,28,CS511,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
323,29,CS500,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
324,29,CS501,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
325,29,CS502,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
326,29,CS503,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
327,29,CS504,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
328,29,CS505,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
329,29,CS506,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
330,29,CS507,Project Management,Required,4.0,Advanced course in project management,https://example.edu/courses/project-management
331,29,CS508,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
332,29,CS509,Thesis Preparation,Required,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
333,29,CS510,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
334,29,CS511,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
335,29,CS512,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
336,29,CS513,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
337,29,CS514,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
338,30,CS500,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
339,30,CS501,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
340,30,CS502,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
341,30,CS503,Data Structures,Elective,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
342,30,CS504,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
343,30,CS505,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
344,30,CS506,Thesis Preparation,Elective,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
345,30,CS507,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
346,30,CS508,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
347,30,CS509,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
348,30,CS510,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
349,30,CS511,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
350,31,CS500,Statistics for Engineers,Core,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
351,31,CS501,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
352,31,CS502,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
353,31,CS503,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
354,31,CS504,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
355,31,CS505,Thesis Preparation,Core,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
356,31,CS506,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
357,31,CS507,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
358,32,CS500,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
359,32,CS501,Computer Graphics,Required,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
360,32,CS502,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
361,32,CS503,Cybersecurity,Elective,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
362,32,CS504,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
363,32,CS505,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
364,32,CS506,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
365,32,CS507,Statistics for Engineers,Core,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
366,32,CS508,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
367,32,CS509,Thesis Preparation,Required,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
368,32,CS510,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
369,32,CS511,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
370,32,CS512,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
371,32,CS513,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
372,33,CS500,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
373,33,CS501,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
374,33,CS502,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
375,33,CS503,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
376,33,CS504,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
377,33,CS505,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
378,33,CS506,Computer Graphics,Required,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
379,33,CS507,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
380,34,CS500,Artificial Intelligence,Elective,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
381,34,CS501,Operating Systems,Core,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
382,34,CS502,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
383,34,CS503,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
384,34,CS504,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
385,34,CS505,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
386,34,CS506,Data Structures,Core,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
387,34,CS507,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
388,34,CS508,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
389,34,CS509,Advanced Mathematics,Required,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
390,34,CS510,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
391,35,CS500,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
392,35,CS501,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
393,35,CS502,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
394,35,CS503,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
395,35,CS504,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
396,35,CS505,Research Methods,Core,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
397,35,CS506,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
398,35,CS507,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
399,35,CS508,Software Engineering,Elective,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
400,35,CS509,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
401,36,CS500,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
402,36,CS501,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
403,36,CS502,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
404,36,CS503,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
405,36,CS504,Software Engineering,Elective,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
406,36,CS505,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
407,36,CS506,Thesis Preparation,Required,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
408,36,CS507,Artificial Intelligence,Elective,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
409,36,CS508,Computer Graphics,Core,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
410,37,CS500,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
411,37,CS501,Artificial Intelligence,Required,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
412,37,CS502,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
413,37,CS503,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
414,37,CS504,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
415,37,CS505,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
416,37,CS506,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
417,37,CS507,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
418,37,CS508,Research Methods,Required,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
419,37,CS509,Cybersecurity,Elective,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
420,37,CS510,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
421,38,CS500,Database Systems,Required,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
422,38,CS501,Project Management,Elective,6.0,Advanced course in project management,https://example.edu/courses/project-management
423,38,CS502,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
424,38,CS503,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
425,38,CS504,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
426,38,CS505,Cybersecurity,Elective,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
427,38,CS506,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
428,38,CS507,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
429,38,CS508,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
430,38,CS509,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
431,38,CS510,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
432,38,CS511,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
433,39,CS500,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
434,39,CS501,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
435,39,CS502,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
436,39,CS503,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
437,39,CS504,Thesis Preparation,Elective,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
438,39,CS505,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
439,39,CS506,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
440,39,CS507,Operating Systems,Core,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
441,39,CS508,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
442,39,CS509,Statistics for Engineers,Core,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
443,39,CS510,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
444,40,CS500,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
445,40,CS501,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
446,40,CS502,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
447,40,CS503,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
448,40,CS504,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
449,40,CS505,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
450,40,CS506,Database Systems,Elective,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
451,40,CS507,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
452,40,CS508,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
453,40,CS509,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
454,41,CS500,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
455,41,CS501,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
456,41,CS502,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
457,41,CS503,Thesis Preparation,Core,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
458,41,CS504,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
459,41,CS505,Advanced Algorithms,Core,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
460,41,CS506,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
461,41,CS507,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
462,41,CS508,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
463,41,CS509,Computer Networks,Core,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
464,41,CS510,Software Engineering,Required,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
465,41,CS511,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
466,41,CS512,Data Structures,Required,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
467,41,CS513,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
468,42,CS500,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
469,42,CS501,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
470,42,CS502,Cybersecurity,Elective,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
471,42,CS503,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
472,42,CS504,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
473,42,CS505,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
474,42,CS506,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
475,42,CS507,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
476,43,CS500,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
477,43,CS501,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
478,43,CS502,Statistics for Engineers,Elective,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
479,43,CS503,Computer Networks,Required,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
480,43,CS504,Database Systems,Elective,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
481,43,CS505,Operating Systems,Core,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
482,43,CS506,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
483,43,CS507,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
484,43,CS508,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
485,43,CS509,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
486,43,CS510,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
487,43,CS511,Cybersecurity,Required,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
488,43,CS512,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
489,43,CS513,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
490,43,CS514,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
491,44,CS500,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
492,44,CS501,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
493,44,CS502,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
494,44,CS503,Thesis Preparation,Core,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
495,44,CS504,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
496,44,CS505,Machine Learning,Required,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
497,44,CS506,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
498,44,CS507,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
499,44,CS508,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
500,44,CS509,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
501,45,CS500,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
502,45,CS501,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
503,45,CS502,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
504,45,CS503,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
505,45,CS504,Computer Graphics,Elective,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
506,45,CS505,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
507,45,CS506,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
508,45,CS507,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
509,45,CS508,Project Management,Elective,6.0,Advanced course in project management,https://example.edu/courses/project-management
510,45,CS509,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
511,45,CS510,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
512,45,CS511,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
513,45,CS512,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
514,45,CS513,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
515,45,CS514,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
516,46,CS500,Operating Systems,Core,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
517,46,CS501,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
518,46,CS502,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
519,46,CS503,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
520,46,CS504,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
521,46,CS505,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
522,46,CS506,Advanced Algorithms,Elective,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
523,46,CS507,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
524,46,CS508,Data Structures,Elective,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
525,46,CS509,Project Management,Required,3.0,Advanced course in project management,https://example.edu/courses/project-management
526,46,CS510,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
527,46,CS511,Artificial Intelligence,Core,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
528,46,CS512,Database Systems,Core,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
529,46,CS513,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
530,46,CS514,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
531,47,CS500,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
532,47,CS501,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
533,47,CS502,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
534,47,CS503,Cybersecurity,Elective,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
535,47,CS504,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
536,47,CS505,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
537,47,CS506,Computer Graphics,Required,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
538,47,CS507,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
539,47,CS508,Operating Systems,Core,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
540,47,CS509,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
541,47,CS510,Thesis Preparation,Core,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
542,47,CS511,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
543,47,CS512,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
544,47,CS513,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
545,48,CS500,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
546,48,CS501,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
547,48,CS502,Thesis Preparation,Elective,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
548,48,CS503,Research Methods,Required,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
549,48,CS504,Cybersecurity,Core,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
550,48,CS505,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
551,48,CS506,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
552,48,CS507,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
553,48,CS508,Computer Networks,Core,3.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
554,48,CS509,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
555,48,CS510,Statistics for Engineers,Core,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
556,48,CS511,Project Management,Required,6.0,Advanced course in project management,https://example.edu/courses/project-management
557,48,CS512,Operating Systems,Elective,4.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
558,48,CS513,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
559,49,CS500,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
560,49,CS501,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
561,49,CS502,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
562,49,CS503,Advanced Algorithms,Required,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
563,49,CS504,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
564,49,CS505,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
565,49,CS506,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
566,49,CS507,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
567,49,CS508,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
568,49,CS509,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
569,50,CS500,Advanced Mathematics,Required,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
570,50,CS501,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
571,50,CS502,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
572,50,CS503,Artificial Intelligence,Required,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
573,50,CS504,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
574,50,CS505,Computer Graphics,Required,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
575,50,CS506,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
576,50,CS507,Thesis Preparation,Elective,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
577,50,CS508,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
578,50,CS509,Cybersecurity,Required,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
579,50,CS510,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
580,51,CS500,Statistics for Engineers,Required,6.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
581,51,CS501,Advanced Algorithms,Core,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
582,51,CS502,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
583,51,CS503,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
584,51,CS504,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
585,51,CS505,Project Management,Core,6.0,Advanced course in project management,https://example.edu/courses/project-management
586,51,CS506,Data Structures,Elective,4.0,Advanced course in data structures,https://example.edu/courses/data-structures
587,51,CS507,Machine Learning,Core,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
588,51,CS508,Computer Graphics,Elective,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
589,51,CS509,Database Systems,Required,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
590,51,CS510,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
591,52,CS500,Artificial Intelligence,Required,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
592,52,CS501,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
593,52,CS502,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
594,52,CS503,Thesis Preparation,Elective,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
595,52,CS504,Statistics for Engineers,Core,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
596,52,CS505,Computer Graphics,Core,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
597,52,CS506,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
598,52,CS507,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
599,52,CS508,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
600,52,CS509,Advanced Mathematics,Elective,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
601,52,CS510,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
602,52,CS511,Database Systems,Elective,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
603,52,CS512,Computer Networks,Required,4.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
604,52,CS513,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
605,52,CS514,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
606,53,CS500,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
607,53,CS501,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
608,53,CS502,Database Systems,Core,6.0,Advanced course in database systems,https://example.edu/courses/database-systems
609,53,CS503,Software Engineering,Required,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
610,53,CS504,Machine Learning,Required,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
611,53,CS505,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
612,53,CS506,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
613,53,CS507,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
614,53,CS508,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
615,54,CS500,Thesis Preparation,Elective,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
616,54,CS501,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
617,54,CS502,Cybersecurity,Elective,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
618,54,CS503,Machine Learning,Elective,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
619,54,CS504,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
620,54,CS505,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
621,54,CS506,Research Methods,Required,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
622,54,CS507,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
623,54,CS508,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
624,54,CS509,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
625,54,CS510,Advanced Algorithms,Elective,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
626,55,CS500,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
627,55,CS501,Statistics for Engineers,Elective,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
628,55,CS502,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
629,55,CS503,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
630,55,CS504,Operating Systems,Elective,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
631,55,CS505,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
632,55,CS506,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
633,55,CS507,Artificial Intelligence,Elective,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
634,55,CS508,Machine Learning,Core,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
635,55,CS509,Advanced Mathematics,Core,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
636,55,CS510,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
637,55,CS511,Database Systems,Core,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
638,56,CS500,Software Engineering,Core,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
639,56,CS501,Statistics for Engineers,Required,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
640,56,CS502,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
641,56,CS503,Computer Graphics,Required,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
642,56,CS504,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
643,56,CS505,Project Management,Core,3.0,Advanced course in project management,https://example.edu/courses/project-management
644,56,CS506,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
645,56,CS507,Data Structures,Required,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
646,57,CS500,Artificial Intelligence,Core,4.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
647,57,CS501,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
648,57,CS502,Advanced Algorithms,Core,6.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
649,57,CS503,Advanced Mathematics,Elective,4.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
650,57,CS504,Operating Systems,Required,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
651,57,CS505,Research Methods,Core,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
652,57,CS506,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
653,57,CS507,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
654,57,CS508,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
655,57,CS509,Computer Graphics,Elective,3.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
656,57,CS510,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
657,57,CS511,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
658,57,CS512,Software Engineering,Core,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
659,57,CS513,Statistics for Engineers,Required,3.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
660,57,CS514,Thesis Preparation,Required,4.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
661,58,CS500,Operating Systems,Required,3.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
662,58,CS501,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
663,58,CS502,Machine Learning,Elective,3.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
664,58,CS503,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
665,58,CS504,Computer Graphics,Core,6.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
666,58,CS505,Database Systems,Elective,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
667,58,CS506,Research Methods,Elective,6.0,Advanced course in research methods,https://example.edu/courses/research-methods
668,58,CS507,Software Engineering,Required,4.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
669,58,CS508,Thesis Preparation,Required,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
670,59,CS500,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
671,59,CS501,Cybersecurity,Elective,6.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
672,59,CS502,Data Structures,Core,3.0,Advanced course in data structures,https://example.edu/courses/data-structures
673,59,CS503,Operating Systems,Elective,6.0,Advanced course in operating systems,https://example.edu/courses/operating-systems
674,59,CS504,Machine Learning,Elective,6.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
675,59,CS505,Database Systems,Elective,4.0,Advanced course in database systems,https://example.edu/courses/database-systems
676,59,CS506,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
677,59,CS507,Thesis Preparation,Core,3.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
678,59,CS508,Computer Networks,Core,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
679,59,CS509,Research Methods,Elective,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
680,59,CS510,Advanced Algorithms,Required,4.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
681,59,CS511,Advanced Mathematics,Core,6.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
682,60,CS500,Computer Networks,Elective,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
683,60,CS501,Research Methods,Core,4.0,Advanced course in research methods,https://example.edu/courses/research-methods
684,60,CS502,Software Engineering,Core,3.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
685,60,CS503,Advanced Algorithms,Required,3.0,Advanced course in advanced algorithms,https://example.edu/courses/advanced-algorithms
686,60,CS504,Cybersecurity,Required,3.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
687,60,CS505,Data Structures,Core,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
688,60,CS506,Project Management,Core,4.0,Advanced course in project management,https://example.edu/courses/project-management
689,60,CS507,Artificial Intelligence,Elective,3.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
690,60,CS508,Statistics for Engineers,Core,4.0,Advanced course in statistics for engineers,https://example.edu/courses/statistics-for-engineers
691,60,CS509,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
692,61,CS500,Research Methods,Elective,3.0,Advanced course in research methods,https://example.edu/courses/research-methods
693,61,CS501,Thesis Preparation,Elective,6.0,Advanced course in thesis preparation,https://example.edu/courses/thesis-preparation
694,61,CS502,Advanced Mathematics,Elective,3.0,Advanced course in advanced mathematics,https://example.edu/courses/advanced-mathematics
695,61,CS503,Machine Learning,Core,4.0,Advanced course in machine learning,https://example.edu/courses/machine-learning
696,61,CS504,Software Engineering,Elective,6.0,Advanced course in software engineering,https://example.edu/courses/software-engineering
697,61,CS505,Computer Graphics,Required,4.0,Advanced course in computer graphics,https://example.edu/courses/computer-graphics
698,61,CS506,Project Management,Elective,3.0,Advanced course in project management,https://example.edu/courses/project-management
699,61,CS507,Data Structures,Required,6.0,Advanced course in data structures,https://example.edu/courses/data-structures
700,61,CS508,Cybersecurity,Core,4.0,Advanced course in cybersecurity,https://example.edu/courses/cybersecurity
701,61,CS509,Computer Networks,Required,6.0,Advanced course in computer networks,https://example.edu/courses/computer-networks
702,61,CS510,Artificial Intelligence,Core,6.0,Advanced course in artificial intelligence,https://example.edu/courses/artificial-intelligence
703,61,CS511,Database Systems,Required,3.0,Advanced course in database systems,https://example.edu/courses/database-systems
