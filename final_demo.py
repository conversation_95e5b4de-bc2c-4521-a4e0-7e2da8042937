"""
QS前1000全球大学Master项目爬虫 - 最终演示
展示完整的功能和数据生成能力
"""

import argparse
import sys
import os
from demo_global_scraper import DemoGlobalScraper
from verify_qs_1000 import main as verify_main


def run_comprehensive_demo():
    """运行综合演示"""
    print("🎓 QS前1000全球大学Master项目爬虫 - 最终演示")
    print("=" * 60)
    
    # 1. 验证QS前1000大学数据
    print("\n📊 步骤1: 验证QS前1000大学数据")
    print("-" * 40)
    if not verify_main():
        print("❌ 数据验证失败，请检查数据文件")
        return False
    
    # 2. 生成演示数据
    print("\n🏗️  步骤2: 生成演示数据")
    print("-" * 40)
    try:
        scraper = DemoGlobalScraper()
        print("正在生成100所全球大学的Master项目数据...")
        scraper.generate_demo_data(100)
        print("✅ 演示数据生成完成!")
    except Exception as e:
        print(f"❌ 演示数据生成失败: {e}")
        return False
    
    # 3. 数据统计
    print("\n📈 步骤3: 数据统计分析")
    print("-" * 40)
    try:
        import pandas as pd
        
        # 读取生成的数据
        universities_df = pd.read_csv('data/universities.csv')
        colleges_df = pd.read_csv('data/colleges.csv')
        programs_df = pd.read_csv('data/programs.csv')
        courses_df = pd.read_csv('data/courses.csv')
        
        print(f"✅ 大学数量: {len(universities_df)}")
        print(f"✅ 学院数量: {len(colleges_df)}")
        print(f"✅ Master项目数量: {len(programs_df)}")
        print(f"✅ 课程数量: {len(courses_df)}")
        
        # 洲分布统计
        print("\n各洲大学分布:")
        continent_stats = universities_df['continent'].value_counts()
        for continent, count in continent_stats.items():
            print(f"  {continent}: {count} 所大学")
        
        # 国家分布统计（前10）
        print("\n主要国家分布 (前10):")
        country_stats = universities_df['country'].value_counts().head(10)
        for country, count in country_stats.items():
            print(f"  {country}: {count} 所大学")
        
        # 学费统计
        fee_stats = programs_df['intl_student_fee'].describe()
        print(f"\n学费统计:")
        print(f"  平均学费: ${fee_stats['mean']:,.2f}")
        print(f"  最低学费: ${fee_stats['min']:,.2f}")
        print(f"  最高学费: ${fee_stats['max']:,.2f}")
        print(f"  学费中位数: ${fee_stats['50%']:,.2f}")
        
        # 热门专业
        print(f"\n热门Master专业 (前5):")
        popular_programs = programs_df['program_name'].value_counts().head(5)
        for i, (program, count) in enumerate(popular_programs.items(), 1):
            print(f"  {i}. {program}: {count} 个项目")
        
    except Exception as e:
        print(f"❌ 数据统计失败: {e}")
        return False
    
    # 4. 运行分析
    print("\n📊 步骤4: 运行数据分析")
    print("-" * 40)
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'analyze_data.py'], 
                              capture_output=True, text=True, cwd='.')
        if result.returncode == 0:
            print("✅ 数据分析完成，报告已生成")
            print("📄 分析报告: data/analysis_report.md")
        else:
            print(f"⚠️  数据分析完成但有警告: {result.stderr}")
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
    
    # 5. 文件大小统计
    print("\n💾 步骤5: 文件大小统计")
    print("-" * 40)
    files_to_check = [
        'data/qs_global_universities.json',
        'data/universities.csv',
        'data/colleges.csv', 
        'data/programs.csv',
        'data/courses.csv',
        'data/analysis_report.md'
    ]
    
    total_size = 0
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            print(f"  {os.path.basename(file_path)}: {size / 1024:.1f} KB")
        else:
            print(f"  {os.path.basename(file_path)}: 文件不存在")
    
    print(f"  总计: {total_size / 1024:.1f} KB")
    
    # 6. 功能验证
    print("\n🧪 步骤6: 功能验证")
    print("-" * 40)
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'test_global_scraper.py'], 
                              capture_output=True, text=True, cwd='.')
        if result.returncode == 0:
            print("✅ 所有功能测试通过")
        else:
            print(f"❌ 功能测试失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 功能验证失败: {e}")
    
    # 总结
    print("\n🎉 演示完成总结")
    print("=" * 60)
    print("✅ QS前1000全球大学数据验证通过")
    print("✅ 演示数据生成成功")
    print("✅ 数据结构完整，包含四层关系")
    print("✅ 支持全球6个洲的大学")
    print("✅ 专注Master学位项目")
    print("✅ CSV格式数据导出")
    print("✅ 数据分析和报告生成")
    
    print(f"\n📁 生成的文件:")
    print(f"  - QS前1000大学数据: data/qs_global_universities.json")
    print(f"  - 大学信息: data/universities.csv")
    print(f"  - 学院信息: data/colleges.csv")
    print(f"  - Master项目: data/programs.csv")
    print(f"  - 课程信息: data/courses.csv")
    print(f"  - 分析报告: data/analysis_report.md")
    
    print(f"\n🚀 使用建议:")
    print(f"  1. 使用演示版本进行快速测试和验证")
    print(f"  2. 根据需要调整大学数量和专业类型")
    print(f"  3. 可以扩展支持更多国家和地区")
    print(f"  4. 实际爬取时请注意合规使用")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='QS前1000全球大学Master项目爬虫最终演示')
    parser.add_argument('--quick', action='store_true', help='快速演示模式（50所大学）')
    parser.add_argument('--full', action='store_true', help='完整演示模式（100所大学）')
    
    args = parser.parse_args()
    
    if args.quick:
        print("🚀 快速演示模式")
        # 生成50所大学的数据
        scraper = DemoGlobalScraper()
        scraper.generate_demo_data(50)
    elif args.full:
        print("🎯 完整演示模式")
        # 运行完整演示
        success = run_comprehensive_demo()
        sys.exit(0 if success else 1)
    else:
        # 默认运行完整演示
        success = run_comprehensive_demo()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
