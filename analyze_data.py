"""
数据分析脚本 - 分析生成的QS美国大学数据
"""

import pandas as pd
from pathlib import Path

# 可选的可视化库
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False


def load_data():
    """加载CSV数据"""
    data_dir = Path('data')

    universities = pd.read_csv(data_dir / 'universities.csv')
    programs = pd.read_csv(data_dir / 'programs.csv')
    courses = pd.read_csv(data_dir / 'courses.csv')

    return universities, programs, courses


def analyze_universities(universities):
    """分析大学数据"""
    print("=" * 60)
    print("大学数据分析")
    print("=" * 60)

    print(f"总大学数量: {len(universities)}")
    print(f"QS排名范围: {universities['qs_ranking'].min()} - {universities['qs_ranking'].max()}")

    print("\nQS前10大学:")
    top_10 = universities.nsmallest(10, 'qs_ranking')[['name', 'qs_ranking']]
    for idx, row in top_10.iterrows():
        print(f"  {row['qs_ranking']:2d}. {row['name']}")


def analyze_programs(programs, universities):
    """分析专业数据"""
    print("\n" + "=" * 60)
    print("专业数据分析")
    print("=" * 60)

    print(f"总专业数量: {len(programs)}")
    print(f"平均每所大学专业数: {len(programs) / len(universities):.1f}")

    # 学费分析
    print(f"\n学费统计:")
    print(f"  平均学费: ${programs['intl_student_fee'].mean():,.2f}")
    print(f"  最低学费: ${programs['intl_student_fee'].min():,.2f}")
    print(f"  最高学费: ${programs['intl_student_fee'].max():,.2f}")
    print(f"  学费中位数: ${programs['intl_student_fee'].median():,.2f}")

    # GPA要求分析
    print(f"\nGPA要求统计:")
    print(f"  平均GPA要求: {programs['gpa_requirement'].mean():.2f}")
    print(f"  最低GPA要求: {programs['gpa_requirement'].min():.1f}")
    print(f"  最高GPA要求: {programs['gpa_requirement'].max():.1f}")

    # 热门专业
    print(f"\n热门专业 (出现频次):")
    popular_programs = programs['name'].value_counts().head(10)
    for program, count in popular_programs.items():
        print(f"  {program}: {count} 个项目")

    # 学制分析
    print(f"\n学制分布:")
    duration_counts = programs['duration'].value_counts()
    for duration, count in duration_counts.items():
        print(f"  {duration}: {count} 个项目")


def analyze_courses(courses, programs):
    """分析课程数据"""
    print("\n" + "=" * 60)
    print("课程数据分析")
    print("=" * 60)

    print(f"总课程数量: {len(courses)}")
    print(f"平均每个专业课程数: {len(courses) / len(programs):.1f}")

    # 课程类型分析
    print(f"\n课程类型分布:")
    course_types = courses['course_type'].value_counts()
    for course_type, count in course_types.items():
        print(f"  {course_type}: {count} 门课程")

    # 学分分析
    print(f"\n学分统计:")
    print(f"  平均学分: {courses['credit'].mean():.1f}")
    print(f"  最低学分: {courses['credit'].min():.1f}")
    print(f"  最高学分: {courses['credit'].max():.1f}")

    # 学分分布
    print(f"\n学分分布:")
    credit_counts = courses['credit'].value_counts().sort_index()
    for credit, count in credit_counts.items():
        print(f"  {credit} 学分: {count} 门课程")


def create_visualizations(universities, programs, courses):
    """创建数据可视化"""
    if not VISUALIZATION_AVAILABLE:
        print("\n⚠️  matplotlib 未安装，跳过图表生成")
        return

    print("\n" + "=" * 60)
    print("生成数据可视化图表...")
    print("=" * 60)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('QS美国大学数据分析', fontsize=16, fontweight='bold')

    # 1. QS排名分布
    axes[0, 0].hist(universities['qs_ranking'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('QS排名分布')
    axes[0, 0].set_xlabel('QS排名')
    axes[0, 0].set_ylabel('大学数量')

    # 2. 学费分布
    axes[0, 1].hist(programs['intl_student_fee'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 1].set_title('国际学生学费分布')
    axes[0, 1].set_xlabel('学费 (USD)')
    axes[0, 1].set_ylabel('专业数量')
    axes[0, 1].ticklabel_format(style='plain', axis='x')

    # 3. 热门专业
    popular_programs = programs['name'].value_counts().head(8)
    axes[1, 0].bar(range(len(popular_programs)), popular_programs.values, color='coral')
    axes[1, 0].set_title('热门专业分布')
    axes[1, 0].set_xlabel('专业')
    axes[1, 0].set_ylabel('项目数量')
    axes[1, 0].set_xticks(range(len(popular_programs)))
    axes[1, 0].set_xticklabels(popular_programs.index, rotation=45, ha='right')

    # 4. 课程类型分布
    course_types = courses['course_type'].value_counts()
    axes[1, 1].pie(course_types.values, labels=course_types.index, autopct='%1.1f%%', startangle=90)
    axes[1, 1].set_title('课程类型分布')

    plt.tight_layout()

    # 保存图表
    output_path = 'data/analysis_charts.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"图表已保存到: {output_path}")

    # 显示图表（如果在支持的环境中）
    try:
        plt.show()
    except:
        print("无法显示图表，但已保存到文件")


def generate_summary_report(universities, programs, courses):
    """生成汇总报告"""
    print("\n" + "=" * 60)
    print("生成汇总报告...")
    print("=" * 60)

    report = f"""
# QS前500美国大学数据分析报告

## 数据概览
- **大学数量**: {len(universities)} 所
- **专业数量**: {len(programs)} 个
- **课程数量**: {len(courses)} 门

## 大学排名分析
- **QS排名范围**: {universities['qs_ranking'].min()} - {universities['qs_ranking'].max()}
- **平均排名**: {universities['qs_ranking'].mean():.1f}

## 专业学费分析
- **平均学费**: ${programs['intl_student_fee'].mean():,.2f}
- **学费范围**: ${programs['intl_student_fee'].min():,.2f} - ${programs['intl_student_fee'].max():,.2f}
- **学费中位数**: ${programs['intl_student_fee'].median():,.2f}

## GPA要求分析
- **平均GPA要求**: {programs['gpa_requirement'].mean():.2f}
- **GPA要求范围**: {programs['gpa_requirement'].min():.1f} - {programs['gpa_requirement'].max():.1f}

## 热门专业 (前5名)
"""

    popular_programs = programs['name'].value_counts().head(5)
    for i, (program, count) in enumerate(popular_programs.items(), 1):
        report += f"{i}. {program}: {count} 个项目\n"

    report += f"""
## 学制分布
"""
    duration_counts = programs['duration'].value_counts()
    for duration, count in duration_counts.items():
        report += f"- {duration}: {count} 个项目\n"

    report += f"""
## 课程分析
- **平均每专业课程数**: {len(courses) / len(programs):.1f} 门
- **平均学分**: {courses['credit'].mean():.1f}

## 课程类型分布
"""
    course_types = courses['course_type'].value_counts()
    for course_type, count in course_types.items():
        report += f"- {course_type}: {count} 门课程 ({count/len(courses)*100:.1f}%)\n"

    report += f"""
---
*报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

    # 保存报告
    report_path = 'data/analysis_report.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"分析报告已保存到: {report_path}")


def main():
    """主函数"""
    print("QS美国大学数据分析工具")
    print("=" * 60)

    try:
        # 加载数据
        universities, programs, courses = load_data()
        print("✅ 数据加载成功")

        # 执行分析
        analyze_universities(universities)
        analyze_programs(programs, universities)
        analyze_courses(courses, programs)

        # 生成可视化（可选）
        create_visualizations(universities, programs, courses)

        # 生成报告
        generate_summary_report(universities, programs, courses)

        print("\n🎉 数据分析完成!")

    except FileNotFoundError as e:
        print(f"❌ 数据文件未找到: {e}")
        print("请先运行 demo_scraper.py 生成数据")
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")


if __name__ == "__main__":
    main()
