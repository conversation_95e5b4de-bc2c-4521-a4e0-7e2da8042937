"""
测试脚本 - 验证爬虫功能
"""

import os
import sys
import logging
from scrapers.qs_ranking_scraper import QSRankingScraper
from scrapers.university_scraper import UniversityScraper


def test_qs_scraper():
    """测试QS排名爬虫"""
    print("=" * 50)
    print("测试QS排名爬虫")
    print("=" * 50)
    
    scraper = QSRankingScraper()
    
    # 获取大学列表
    universities = scraper.get_qs_us_universities()
    print(f"获取到 {len(universities)} 所美国大学")
    
    # 显示前5所大学
    print("\n前5所大学:")
    for i, uni in enumerate(universities[:5], 1):
        print(f"{i}. {uni['name']} (QS排名: {uni['qs_ranking']})")
    
    # 测试排名过滤
    top_10 = scraper.filter_by_ranking(10)
    print(f"\nQS前10美国大学数量: {len(top_10)}")
    
    # 保存到文件
    scraper.save_universities_list()
    print("大学列表已保存到 data/qs_us_universities.json")
    
    return True


def test_university_scraper():
    """测试大学信息爬虫（简化版）"""
    print("\n" + "=" * 50)
    print("测试大学信息爬虫")
    print("=" * 50)
    
    try:
        # 创建一个简化的测试
        scraper = UniversityScraper()
        
        # 获取QS排名数据
        universities_list = scraper.qs_scraper.get_qs_us_universities()
        print(f"QS排名爬虫获取到 {len(universities_list)} 所大学")
        
        # 测试单个大学信息提取（不实际访问网站）
        test_university = universities_list[0]  # MIT
        print(f"\n测试大学: {test_university['name']}")
        print(f"QS排名: {test_university['qs_ranking']}")
        print(f"网站: {test_university['website']}")
        
        # 模拟数据结构
        university_id = 1
        university_data = {
            'university_id': university_id,
            'name': test_university['name'],
            'country': test_university['country'],
            'continent': test_university['continent'],
            'qs_ranking': test_university['qs_ranking'],
            'website': test_university['website']
        }
        
        print(f"\n生成的大学数据结构:")
        for key, value in university_data.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_data_structure():
    """测试数据结构"""
    print("\n" + "=" * 50)
    print("测试数据结构")
    print("=" * 50)
    
    # 模拟完整的数据结构
    sample_university = {
        'university_id': 1,
        'name': 'Massachusetts Institute of Technology',
        'country': 'United States',
        'continent': 'North America',
        'qs_ranking': 1,
        'website': 'https://web.mit.edu'
    }
    
    sample_program = {
        'program_id': 101,
        'university_id': 1,
        'name': 'Computer Science',
        'related_majors': 'CS,Software Engineering,AI',
        'duration': '4 years',
        'course_url': 'https://web.mit.edu/academics/computer-science',
        'gpa_requirement': 3.8,
        'intl_student_fee': 55000.00,
        'language_req': 'TOEFL 100 / IELTS 7.0',
        'intake_months': 'Fall 2025',
        'application_deadline': '2025-01-01'
    }
    
    sample_course = {
        'course_id': 5001,
        'program_id': 101,
        'course_code': 'CS101',
        'course_name': 'Introduction to Computer Science',
        'course_type': 'Core',
        'credit': 4.0,
        'description': 'Fundamental concepts in computer science',
        'course_url': 'https://web.mit.edu/courses/cs101'
    }
    
    print("大学数据结构示例:")
    for key, value in sample_university.items():
        print(f"  {key}: {value}")
    
    print("\n专业数据结构示例:")
    for key, value in sample_program.items():
        print(f"  {key}: {value}")
    
    print("\n课程数据结构示例:")
    for key, value in sample_course.items():
        print(f"  {key}: {value}")
    
    return True


def test_csv_export():
    """测试CSV导出功能"""
    print("\n" + "=" * 50)
    print("测试CSV导出功能")
    print("=" * 50)
    
    try:
        import pandas as pd
        
        # 创建示例数据
        universities_data = [
            {
                'university_id': 1,
                'name': 'Massachusetts Institute of Technology',
                'country': 'United States',
                'continent': 'North America',
                'qs_ranking': 1,
                'website': 'https://web.mit.edu'
            },
            {
                'university_id': 2,
                'name': 'Harvard University',
                'country': 'United States',
                'continent': 'North America',
                'qs_ranking': 4,
                'website': 'https://www.harvard.edu'
            }
        ]
        
        programs_data = [
            {
                'program_id': 101,
                'university_id': 1,
                'name': 'Computer Science',
                'related_majors': 'CS,Software Engineering',
                'duration': '4 years',
                'course_url': 'https://web.mit.edu/cs',
                'gpa_requirement': 3.8,
                'intl_student_fee': 55000.00,
                'language_req': 'TOEFL 100',
                'intake_months': 'Fall',
                'application_deadline': '2025-01-01'
            }
        ]
        
        courses_data = [
            {
                'course_id': 5001,
                'program_id': 101,
                'course_code': 'CS101',
                'course_name': 'Introduction to Computer Science',
                'course_type': 'Core',
                'credit': 4.0,
                'description': 'Fundamental concepts',
                'course_url': 'https://web.mit.edu/courses/cs101'
            }
        ]
        
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        
        # 导出到CSV
        df_universities = pd.DataFrame(universities_data)
        df_programs = pd.DataFrame(programs_data)
        df_courses = pd.DataFrame(courses_data)
        
        df_universities.to_csv('data/test_universities.csv', index=False, encoding='utf-8')
        df_programs.to_csv('data/test_programs.csv', index=False, encoding='utf-8')
        df_courses.to_csv('data/test_courses.csv', index=False, encoding='utf-8')
        
        print("测试CSV文件已生成:")
        print("  - data/test_universities.csv")
        print("  - data/test_programs.csv")
        print("  - data/test_courses.csv")
        
        # 验证文件内容
        print(f"\n大学数据: {len(df_universities)} 行")
        print(f"专业数据: {len(df_programs)} 行")
        print(f"课程数据: {len(df_courses)} 行")
        
        return True
        
    except Exception as e:
        print(f"CSV导出测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("QS前500美国大学信息爬虫 - 功能测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    tests = [
        ("QS排名爬虫", test_qs_scraper),
        ("大学信息爬虫", test_university_scraper),
        ("数据结构", test_data_structure),
        ("CSV导出", test_csv_export)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n{test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试都通过了！爬虫程序可以正常使用。")
        print("\n下一步:")
        print("1. 运行 'python main.py --limit 5' 进行小规模测试")
        print("2. 运行 'python main.py' 开始完整爬取")
    else:
        print("\n⚠️  部分测试失败，请检查配置和依赖。")


if __name__ == "__main__":
    main()
