# QS前1000全球大学Master项目信息爬虫 - 使用指南

## 快速开始

### 1. 环境准备

确保你的系统已安装Python 3.12+，然后安装依赖：

```bash
# 使用pip安装
pip install requests beautifulsoup4 selenium pandas lxml

# 或使用poetry（推荐）
poetry install
```

### 2. 运行演示版爬虫（推荐）

演示版爬虫会生成真实的数据结构，但不进行实际的网络爬取，避免网络问题：

```bash
# 生成20所大学的演示数据
python demo_global_scraper.py

# 生成指定数量的大学数据
python demo_global_scraper.py --universities 50

# 详细输出模式
python demo_global_scraper.py --verbose
```

### 3. 数据分析

生成数据后，可以运行分析脚本：

```bash
python analyze_data.py
```

### 4. 测试功能

运行测试脚本验证所有功能：

```bash
python test_scraper.py
```

## 高级使用

### 实际网络爬取（谨慎使用）

如果你想进行实际的网络爬取，可以使用主程序：

```bash
# 小规模测试（推荐先试试）
python main.py --limit 3 --verbose

# 完整爬取（需要很长时间，可能遇到网络问题）
python main.py
```

**注意事项：**
- 实际爬取可能需要几小时到几天时间
- 某些大学网站可能有反爬虫机制
- 建议先用演示版验证数据结构

### 自定义配置

编辑 `config.py` 文件来调整：

- 请求延迟时间
- 超时设置
- User-Agent列表
- 添加更多大学

## 输出文件说明

运行后会在 `data/` 目录生成以下文件：

### CSV数据文件
- `universities.csv` - 大学基本信息
- `programs.csv` - 专业信息
- `courses.csv` - 课程信息

### 分析文件
- `analysis_report.md` - 数据分析报告
- `qs_us_universities.json` - QS大学列表缓存

### 日志文件
- `scraper.log` - 爬虫运行日志

## 数据字段说明

### 大学信息 (universities.csv)
| 字段 | 说明 | 示例 |
|------|------|------|
| university_id | 大学唯一ID | 1 |
| name | 大学名称 | "MIT" |
| country | 国家 | "United States" |
| continent | 所在洲 | "North America" |
| qs_ranking | QS排名 | 1 |
| website | 官网URL | "https://web.mit.edu" |

### 专业信息 (programs.csv)
| 字段 | 说明 | 示例 |
|------|------|------|
| program_id | 专业唯一ID | 101 |
| university_id | 所属大学ID | 1 |
| name | 专业名称 | "Computer Science" |
| related_majors | 相关专业 | "CS,AI,Data Science" |
| duration | 学制 | "2 years" |
| gpa_requirement | GPA要求 | 3.8 |
| intl_student_fee | 国际学生学费 | 55000.00 |
| language_req | 语言要求 | "TOEFL 100" |

### 课程信息 (courses.csv)
| 字段 | 说明 | 示例 |
|------|------|------|
| course_id | 课程唯一ID | 5001 |
| program_id | 所属专业ID | 101 |
| course_code | 课程编号 | "CS101" |
| course_name | 课程名称 | "Intro to CS" |
| course_type | 课程类型 | "Core" |
| credit | 学分 | 4.0 |

## 常见问题

### Q: 为什么推荐使用演示版？
A: 演示版可以：
- 快速生成真实的数据结构
- 避免网络连接问题
- 展示完整的数据分析流程
- 适合开发和测试

### Q: 如何添加更多大学？
A: 编辑 `scrapers/qs_ranking_scraper.py` 中的大学列表，添加新的大学信息。

### Q: 数据准确性如何？
A:
- 演示版数据：结构真实，数值为随机生成
- 实际爬取：依赖于大学官网的实时数据

### Q: 如何处理爬取失败？
A:
- 检查网络连接
- 调整 `config.py` 中的延迟设置
- 查看 `data/scraper.log` 日志文件

### Q: 可以爬取其他国家的大学吗？
A: 可以，修改 `scrapers/qs_ranking_scraper.py` 中的大学列表即可。

## 扩展开发

### 添加新的数据字段

1. 修改 `scrapers/university_scraper.py` 中的提取逻辑
2. 更新 CSV 输出格式
3. 调整分析脚本

### 集成到其他系统

生成的CSV文件可以轻松导入到：
- Excel/Google Sheets
- 数据库系统
- 数据分析工具
- Web应用

### 自动化运行

可以设置定时任务定期更新数据：

```bash
# 添加到crontab（每周运行一次）
0 0 * * 0 cd /path/to/scbot && python demo_scraper.py --universities 50
```

## 技术支持

如果遇到问题：

1. 查看 `data/scraper.log` 日志
2. 运行 `python test_scraper.py` 检查环境
3. 检查网络连接和防火墙设置
4. 确认所有依赖已正确安装

## 法律声明

- 本工具仅供学习和研究使用
- 请遵守各大学网站的robots.txt和使用条款
- 不要过于频繁地访问目标网站
- 使用爬取的数据时请注明来源
