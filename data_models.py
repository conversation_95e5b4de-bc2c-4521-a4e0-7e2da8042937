"""
数据模型定义
定义大学、学院、专业、课程等数据结构
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import date


@dataclass
class University:
    """大学信息数据模型"""
    university_id: int
    name: str
    country: str
    continent: str
    qs_ranking: int
    website: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'university_id': self.university_id,
            'name': self.name,
            'country': self.country,
            'continent': self.continent,
            'qs_ranking': self.qs_ranking,
            'website': self.website
        }


@dataclass
class College:
    """学院信息数据模型"""
    college_id: int
    university_id: int
    college_name: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'college_id': self.college_id,
            'university_id': self.university_id,
            'college_name': self.college_name
        }


@dataclass
class DegreeProgram:
    """学位专业数据模型"""
    program_id: int
    college_id: int
    program_name: str
    degree: str  # Master, Bachelor, PhD等
    duration: Optional[str] = None  # 学制，如"2 years"
    course_url: Optional[str] = None  # 专业课程链接
    gpa_requirement: Optional[float] = None  # 申请最低GPA要求
    intl_student_fee: Optional[float] = None  # 国际学生学费（年）
    language_req: Optional[str] = None  # 语言要求
    intake_months: Optional[str] = None  # 入学时间
    application_deadline: Optional[str] = None  # 申请截止日期
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'program_id': self.program_id,
            'college_id': self.college_id,
            'program_name': self.program_name,
            'degree': self.degree,
            'duration': self.duration,
            'course_url': self.course_url,
            'gpa_requirement': self.gpa_requirement,
            'intl_student_fee': self.intl_student_fee,
            'language_req': self.language_req,
            'intake_months': self.intake_months,
            'application_deadline': self.application_deadline
        }


@dataclass
class Course:
    """课程数据模型"""
    course_id: int
    program_id: int
    course_code: Optional[str] = None  # 课程编号
    course_name: str = ""  # 课程名称
    course_type: Optional[str] = None  # 类型（必修/选修）
    credit: Optional[float] = None  # 学分数
    description: Optional[str] = None  # 课程描述
    course_url: Optional[str] = None  # 课程介绍链接
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'course_id': self.course_id,
            'program_id': self.program_id,
            'course_code': self.course_code,
            'course_name': self.course_name,
            'course_type': self.course_type,
            'credit': self.credit,
            'description': self.description,
            'course_url': self.course_url
        }


class DataManager:
    """数据管理器类"""
    
    def __init__(self):
        """初始化数据管理器"""
        self.universities: List[University] = []
        self.colleges: List[College] = []
        self.programs: List[DegreeProgram] = []
        self.courses: List[Course] = []
        
        # ID计数器
        self.university_id_counter = 1
        self.college_id_counter = 1
        self.program_id_counter = 1
        self.course_id_counter = 1
    
    def add_university(self, name: str, country: str, continent: str, 
                      qs_ranking: int, website: str) -> University:
        """
        添加大学
        
        Args:
            name: 大学名称
            country: 国家
            continent: 洲
            qs_ranking: QS排名
            website: 网站
            
        Returns:
            University对象
        """
        university = University(
            university_id=self.university_id_counter,
            name=name,
            country=country,
            continent=continent,
            qs_ranking=qs_ranking,
            website=website
        )
        
        self.universities.append(university)
        self.university_id_counter += 1
        
        return university
    
    def add_college(self, university_id: int, college_name: str) -> College:
        """
        添加学院
        
        Args:
            university_id: 大学ID
            college_name: 学院名称
            
        Returns:
            College对象
        """
        college = College(
            college_id=self.college_id_counter,
            university_id=university_id,
            college_name=college_name
        )
        
        self.colleges.append(college)
        self.college_id_counter += 1
        
        return college
    
    def add_program(self, college_id: int, program_name: str, degree: str,
                   **kwargs) -> DegreeProgram:
        """
        添加专业
        
        Args:
            college_id: 学院ID
            program_name: 专业名称
            degree: 学位类型
            **kwargs: 其他可选参数
            
        Returns:
            DegreeProgram对象
        """
        program = DegreeProgram(
            program_id=self.program_id_counter,
            college_id=college_id,
            program_name=program_name,
            degree=degree,
            **kwargs
        )
        
        self.programs.append(program)
        self.program_id_counter += 1
        
        return program
    
    def add_course(self, program_id: int, course_name: str, **kwargs) -> Course:
        """
        添加课程
        
        Args:
            program_id: 专业ID
            course_name: 课程名称
            **kwargs: 其他可选参数
            
        Returns:
            Course对象
        """
        course = Course(
            course_id=self.course_id_counter,
            program_id=program_id,
            course_name=course_name,
            **kwargs
        )
        
        self.courses.append(course)
        self.course_id_counter += 1
        
        return course
    
    def get_universities_data(self) -> List[Dict[str, Any]]:
        """获取所有大学数据"""
        return [uni.to_dict() for uni in self.universities]
    
    def get_colleges_data(self) -> List[Dict[str, Any]]:
        """获取所有学院数据"""
        return [college.to_dict() for college in self.colleges]
    
    def get_programs_data(self) -> List[Dict[str, Any]]:
        """获取所有专业数据"""
        return [program.to_dict() for program in self.programs]
    
    def get_courses_data(self) -> List[Dict[str, Any]]:
        """获取所有课程数据"""
        return [course.to_dict() for course in self.courses]
    
    def find_university_by_name(self, name: str) -> Optional[University]:
        """根据名称查找大学"""
        for uni in self.universities:
            if uni.name.lower() == name.lower():
                return uni
        return None
    
    def find_college_by_name(self, university_id: int, college_name: str) -> Optional[College]:
        """根据名称查找学院"""
        for college in self.colleges:
            if (college.university_id == university_id and 
                college.college_name.lower() == college_name.lower()):
                return college
        return None
    
    def get_programs_by_university(self, university_id: int) -> List[DegreeProgram]:
        """获取指定大学的所有专业"""
        university_colleges = [c.college_id for c in self.colleges if c.university_id == university_id]
        return [p for p in self.programs if p.college_id in university_colleges]
    
    def get_master_programs(self) -> List[DegreeProgram]:
        """获取所有Master学位专业"""
        return [p for p in self.programs if p.degree.lower() == 'master']
    
    def clear_all_data(self):
        """清空所有数据"""
        self.universities.clear()
        self.colleges.clear()
        self.programs.clear()
        self.courses.clear()
        
        # 重置计数器
        self.university_id_counter = 1
        self.college_id_counter = 1
        self.program_id_counter = 1
        self.course_id_counter = 1
