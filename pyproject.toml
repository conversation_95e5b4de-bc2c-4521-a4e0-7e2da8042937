[project]
name = "scbot"
version = "0.1.0"
description = "Study Abroad Assistant"
authors = [
    {name = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langgraph (>=0.4.7,<0.5.0)",
    "langchain-openai (>=0.3.18,<0.4.0)",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "selenium>=4.15.0",
    "pandas>=2.1.0",
    "lxml>=4.9.0"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
